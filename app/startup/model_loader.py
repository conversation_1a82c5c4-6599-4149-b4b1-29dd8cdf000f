"""
Model loader for preloading sentence transformers at application startup.

This module handles loading and caching of the sentence transformer model
to avoid loading delays during runtime.
"""

import logging
import time
from typing import Optional

from sentence_transformers import SentenceTransformer
from ..shared.config.base import get_settings

logger = logging.getLogger(__name__)

class ModelLoader:
    """Handles preloading and caching of sentence transformer models."""
    
    _instance: Optional['ModelLoader'] = None
    _model: Optional[SentenceTransformer] = None
    _model_name: Optional[str] = None
    _is_loaded: bool = False
    
    def __new__(cls) -> 'ModelLoader':
        """Singleton pattern to ensure only one model loader instance."""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    @classmethod
    def get_instance(cls) -> 'ModelLoader':
        """Get the singleton instance of ModelLoader."""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def load_model(self, force_reload: bool = False) -> SentenceTransformer:
        """
        Load the sentence transformer model.
        
        Args:
            force_reload: Whether to force reload the model even if already loaded
            
        Returns:
            The loaded SentenceTransformer model
        """
        # Get model name from configuration if not set
        if self._model_name is None:
            settings = get_settings()
            self._model_name = settings.mem0.embedding_model.replace("sentence-transformers/", "")
            
        if self._model is not None and not force_reload:
            logger.info(f"📦 Model {self._model_name} already loaded, returning cached instance")
            return self._model
            
        logger.info(f"🔄 Loading sentence transformer model: {self._model_name}")
        start_time = time.time()
        
        try:
            # Load the model
            self._model = SentenceTransformer(self._model_name)
            load_time = time.time() - start_time
            self._is_loaded = True
            
            logger.info(f"✅ Model loaded successfully in {load_time:.2f} seconds")
            logger.info(f"   📏 Model dimensions: {self._model.get_sentence_embedding_dimension()}")
            logger.info(f"   🏠 Model device: {self._model.device}")
            logger.info(f"   📦 Model name: {self._model_name}")
            
            return self._model
            
        except Exception as e:
            logger.error(f"❌ Failed to load model {self._model_name}: {e}")
            self._is_loaded = False
            raise
    
    def get_model(self) -> Optional[SentenceTransformer]:
        """Get the loaded model without loading it."""
        return self._model
    
    def is_loaded(self) -> bool:
        """Check if the model is loaded."""
        return self._is_loaded and self._model is not None
    
    def get_model_info(self) -> dict:
        """Get information about the loaded model."""
        if not self.is_loaded():
            return {"error": "Model not loaded"}
        
        return {
            "model_name": self._model_name,
            "dimensions": self._model.get_sentence_embedding_dimension(),
            "device": str(self._model.device),
            "max_seq_length": getattr(self._model, 'max_seq_length', 'Unknown'),
            "is_loaded": self._is_loaded
        }

# Global instance
model_loader = ModelLoader()

def preload_sentence_transformer() -> SentenceTransformer:
    """
    Preload the sentence transformer model at application startup.
    
    Returns:
        The loaded SentenceTransformer model
    """
    logger.info("🚀 PRELOADING SENTENCE TRANSFORMER MODEL")
    logger.info("=" * 60)
    
    try:
        model = model_loader.load_model()
        info = model_loader.get_model_info()
        
        logger.info("📊 MODEL INFORMATION")
        logger.info(f"   📦 Name: {info['model_name']}")
        logger.info(f"   📏 Dimensions: {info['dimensions']}")
        logger.info(f"   🏠 Device: {info['device']}")
        logger.info(f"   📝 Max Sequence Length: {info['max_seq_length']}")
        logger.info("=" * 60)
        
        return model
        
    except Exception as e:
        logger.error(f"❌ Failed to preload sentence transformer: {e}")
        raise


def get_preloaded_model() -> Optional[SentenceTransformer]:
    """
    Get the preloaded sentence transformer model.
    
    Returns:
        The preloaded model or None if not loaded
    """
    return model_loader.get_model()


def ensure_model_loaded() -> SentenceTransformer:
    """
    Ensure the model is loaded, loading it if necessary.
    
    Returns:
        The loaded SentenceTransformer model
    """
    if not model_loader.is_loaded():
        logger.warning("⚠️  Model not preloaded, loading now...")
        return model_loader.load_model()
    return model_loader.get_model()
