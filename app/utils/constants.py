from enum import Enum


# Enum for SSE event types based on chat response structure
class SSEEventType(str, Enum):
    """Server-Sent Events types for real-time communication."""

    # Session management events
    SESSION_INITIALIZED = "session_initialized"

    # Message streaming events
    MESSAGE_STREAM_STARTED = "message_stream_started"
    MESSAGE_STREAMING = "message_streaming"
    MESSAGE_END = "message_end"

    MESSAGE_RESPONSE = "message_response"

    # MCP (Model Context Protocol) events
    MCP_EXECUTION_STARTED = "mcp_execution_started"
    MCP_EXECUTION_ENDED = "mcp_execution_ended"
    MCP_EXECUTION_FAILED = "mcp_execution_failed"

    # Workflow execution events
    WORKFLOW_EXECUTION_STARTED = "workflow_execution_started"
    WORKFLOW_EXECUTION_STEP = "workflow_execution_step"
    WORKFLOW_EXECUTION_COMPLETED = "workflow_execution_completed"
    WORKFLOW_EXECUTION_FAILED = "workflow_execution_failed"

    KN<PERSON><PERSON>DGE_FETCH_STARTED = "knowledge_fetch_started"
    KNOWLEDGE_FETCH_COMPLETED = "knowledge_fetch_completed"
    KNOWLEDGE_FETCH_FAILED = "knowledge_fetch_failed"

    # System events
    KEEP_ALIVE = "keep_alive"
    ERROR = "error"
    CONNECTION_CLOSED = "connection_closed"


class AgentType(Enum):
    GLOBAL_AGENT = "global_agent"
    PERSONA_AGENT = "persona_agent"
    USER_AGENT = "user_agent"
    AI_AGENT = "ai_agent"
    HEAD_AGENT = "head_agent"


class ErrorCode(str, Enum):
    """Error codes for standardized error handling across the application."""

    # General errors (1000-1099)
    UNKNOWN_ERROR = "ERR_1000"
    VALIDATION_ERROR = "ERR_1001"
    CONFIGURATION_ERROR = "ERR_1002"
    TIMEOUT_ERROR = "ERR_1003"
    NETWORK_ERROR = "ERR_1004"

    # Kafka errors (1100-1199)
    KAFKA_MESSAGE_PARSE_ERROR = "ERR_1100"
    KAFKA_PRODUCER_ERROR = "ERR_1101"
    KAFKA_CONSUMER_ERROR = "ERR_1102"
    KAFKA_TOPIC_ERROR = "ERR_1103"

    # Agent errors (1200-1299)
    AGENT_NOT_FOUND = "ERR_1200"
    AGENT_CONFIG_INVALID = "ERR_1201"
    AGENT_CREATION_FAILED = "ERR_1202"
    AGENT_INITIALIZATION_FAILED = "ERR_1203"
    AGENT_EXECUTION_FAILED = "ERR_1204"

    # Session errors (1300-1399)
    SESSION_NOT_FOUND = "ERR_1300"
    SESSION_CREATION_FAILED = "ERR_1301"
    SESSION_DELETION_FAILED = "ERR_1302"
    SESSION_EXPIRED = "ERR_1303"
    SESSION_INVALID_STATE = "ERR_1304"
    SESSION_STOP_ERROR = "ERR_1305"

    # Chat errors (1400-1499)
    CHAT_PROCESSING_FAILED = "ERR_1400"
    CHAT_MESSAGE_INVALID = "ERR_1401"
    CHAT_STREAM_ERROR = "ERR_1402"
    CHAT_CONTEXT_ERROR = "ERR_1403"

    # Group chat errors (1500-1599)
    GROUP_CHAT_NOT_FOUND = "ERR_1500"
    GROUP_CHAT_CREATION_FAILED = "ERR_1501"
    GROUP_CHAT_PROCESSING_FAILED = "ERR_1502"

    # Orchestration errors (1600-1699)
    ORCHESTRATION_SESSION_FAILED = "ERR_1600"
    ORCHESTRATION_CHAT_FAILED = "ERR_1601"
    ORCHESTRATION_TEAM_ERROR = "ERR_1602"

    # MCP errors (1700-1799)
    MCP_TOOL_ERROR = "ERR_1700"
    MCP_FETCH_FAILED = "ERR_1701"
    MCP_EXECUTION_ERROR = "ERR_1702"

    # Workflow errors (1800-1899)
    WORKFLOW_NOT_FOUND = "ERR_1800"
    WORKFLOW_EXECUTION_FAILED = "ERR_1801"
    WORKFLOW_FETCH_FAILED = "ERR_1802"

    # Human input errors (1900-1999)
    HUMAN_INPUT_TIMEOUT = "ERR_1900"
    HUMAN_INPUT_INVALID = "ERR_1901"
    HUMAN_INPUT_PROCESSING_FAILED = "ERR_1902"


class ErrorMessage:
    """User-friendly error messages for frontend display and understanding."""

    # General errors (1000-1099)
    UNKNOWN_ERROR = (
        "Something went wrong. Please try again or contact support if the "
        "issue persists."
    )
    VALIDATION_ERROR = (
        "The information provided is invalid. Please check your input and " "try again."
    )
    CONFIGURATION_ERROR = (
        "There's a configuration issue. Please contact your administrator."
    )
    TIMEOUT_ERROR = "The request took too long to complete. Please try again."
    NETWORK_ERROR = (
        "Unable to connect to the service. Please check your internet "
        "connection and try again."
    )

    # Kafka errors (1100-1199)
    KAFKA_MESSAGE_PARSE_ERROR = (
        "Unable to process the message. Please try sending your request " "again."
    )
    KAFKA_PRODUCER_ERROR = "Unable to send your message. Please try again in a moment."
    KAFKA_CONSUMER_ERROR = (
        "Unable to receive messages. Please refresh the page and try again."
    )
    KAFKA_TOPIC_ERROR = (
        "Service communication error. Please contact support if this " "continues."
    )

    # Agent errors (1200-1299)
    AGENT_NOT_FOUND = (
        "The requested agent could not be found. Please check the agent ID "
        "and try again."
    )
    AGENT_CONFIG_INVALID = (
        "The agent configuration is invalid. Please contact your " "administrator."
    )
    AGENT_CREATION_FAILED = (
        "Unable to create the agent. Please try again or contact support."
    )
    AGENT_INITIALIZATION_FAILED = (
        "The agent failed to start properly. Please try again."
    )
    AGENT_EXECUTION_FAILED = (
        "The agent encountered an error while processing your request. "
        "Please try again."
    )

    # Session errors (1300-1399)
    SESSION_NOT_FOUND = (
        "Your session has expired or could not be found. Please start a new "
        "conversation."
    )
    SESSION_CREATION_FAILED = (
        "Unable to start a new session. Please refresh the page and try again."
    )
    SESSION_DELETION_FAILED = (
        "Unable to end the session properly. You may need to refresh the page."
    )
    SESSION_EXPIRED = (
        "Your session has expired for security reasons. Please start a new "
        "conversation."
    )
    SESSION_INVALID_STATE = (
        "Your session is in an invalid state. Please start a new conversation."
    )
    SESSION_STOP_ERROR = "Unable to stop the current session. Please refresh the page."

    # Chat errors (1400-1499)
    CHAT_PROCESSING_FAILED = (
        "Unable to process your message. Please try rephrasing or try again " "later."
    )
    CHAT_MESSAGE_INVALID = (
        "Your message format is invalid. Please check your input and try again."
    )
    CHAT_STREAM_ERROR = (
        "Connection interrupted while receiving the response. Please try again."
    )
    CHAT_CONTEXT_ERROR = (
        "Unable to maintain conversation context. Please start a new " "conversation."
    )

    # Group chat errors (1500-1599)
    GROUP_CHAT_NOT_FOUND = (
        "The group conversation could not be found. Please check the group ID."
    )
    GROUP_CHAT_CREATION_FAILED = (
        "Unable to create the group conversation. Please try again."
    )
    GROUP_CHAT_PROCESSING_FAILED = (
        "Unable to process the group message. Please try again."
    )

    # Orchestration errors (1600-1699)
    ORCHESTRATION_SESSION_FAILED = (
        "Unable to start the team collaboration session. Please try again."
    )
    ORCHESTRATION_CHAT_FAILED = (
        "The team collaboration encountered an error. Please try again."
    )
    ORCHESTRATION_TEAM_ERROR = (
        "There's an issue with the team setup. Please contact your " "administrator."
    )

    # MCP errors (1700-1799)
    MCP_TOOL_ERROR = (
        "A tool encountered an error while processing your request. Please "
        "try again."
    )
    MCP_FETCH_FAILED = "Unable to retrieve the required tools. Please try again later."
    MCP_EXECUTION_ERROR = (
        "The tool failed to execute properly. Please try a different approach."
    )

    # Workflow errors (1800-1899)
    WORKFLOW_NOT_FOUND = (
        "The requested workflow could not be found. Please check the " "workflow ID."
    )
    WORKFLOW_EXECUTION_FAILED = (
        "The workflow encountered an error during execution. Please try again."
    )
    WORKFLOW_FETCH_FAILED = "Unable to load the workflow. Please try again later."

    # Human input errors (1900-1999)
    HUMAN_INPUT_TIMEOUT = (
        "Your response took too long. Please try again with a quicker " "response."
    )
    HUMAN_INPUT_INVALID = (
        "Your input format is invalid. Please check the requirements and " "try again."
    )
    HUMAN_INPUT_PROCESSING_FAILED = "Unable to process your input. Please try again."

    # Legacy error messages (for backward compatibility)
    # Agent creation errors
    AGENT_CONFIG_NOT_FOUND = AGENT_NOT_FOUND
    AGENT_SESSION_CREATION_FAILED = SESSION_CREATION_FAILED
    GROUP_CHAT_SESSION_CREATION_FAILED = GROUP_CHAT_CREATION_FAILED

    # Chat processing errors
    CHAT_SESSION_NOT_FOUND = SESSION_NOT_FOUND
    CHAT_PROCESSING_ERROR = CHAT_PROCESSING_FAILED
    CHAT_STREAM_ERROR = CHAT_STREAM_ERROR

    # Session management errors
    SESSION_DELETION_ERROR = SESSION_DELETION_FAILED
    SESSION_STOP_ERROR = SESSION_STOP_ERROR

    # Orchestration errors
    ORCHESTRATION_SESSION_ERROR = ORCHESTRATION_SESSION_FAILED
    ORCHESTRATION_CHAT_ERROR = ORCHESTRATION_CHAT_FAILED

    # Human input errors
    HUMAN_INPUT_ERROR = HUMAN_INPUT_PROCESSING_FAILED

    # General errors
    MESSAGE_PARSE_ERROR = KAFKA_MESSAGE_PARSE_ERROR
    UNKNOWN_TOPIC_ERROR = KAFKA_TOPIC_ERROR
