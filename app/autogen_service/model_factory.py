import asyncio
import logging
import os

# Create SSL context that's more permissive for development
import ssl
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

import aiohttp
from autogen_core.models import ModelFamily, validate_model_info
from autogen_ext.models.anthropic import AnthropicChatCompletionClient
from autogen_ext.models.ollama import OllamaChatCompletionClient
from autogen_ext.models.openai import OpenAIChatCompletionClient

from app.shared.config.base import Settings

from ..shared.config.base import get_settings

# Import additional model clients for comprehensive support


logger = logging.getLogger(__name__)

settings: Settings = get_settings()

# Cache for Requesty models to avoid frequent API calls
_requesty_models_cache = None
_cache_timestamp = None
_cache_duration = timedelta(hours=1)  # Cache for 1 hour

# Cache for OpenRouter models to avoid frequent API calls
_openrouter_models_cache = None
_openrouter_cache_timestamp = None


class ModelFactory:
    """Factory class to create model clients based on configuration."""

    @staticmethod
    def create_model_client(model_config: Dict[str, Any]) -> Optional[Any]:
        """
        Creates a model client based on the provided configuration.
        Supports all AutoGen model providers including OpenAI, Anthropic, Azure, Ollama, and Requesty routing.

        Args:
            model_config: Dictionary containing model configuration parameters
                - provider: The model provider (e.g., "OpenAIChatCompletionClient")
                - model: The model name (e.g., "gpt-4o", "anthropic/claude-3-sonnet")
                - api_key: API key (optional if set in environment)
                - temperature: Temperature setting (optional)
                - max_tokens: Maximum tokens (optional)
                - model_info: Model capabilities (optional)
                - base_url: Base URL for API (optional)
                - azure_deployment: Azure deployment name (for Azure models)
                - azure_endpoint: Azure endpoint URL (for Azure models)
                - api_version: API version (for Azure models)

        Returns:
            A model client instance or None if creation fails
        """
        try:
            provider = model_config.get("provider", "OpenAIChatCompletionClient")
            llm_type = model_config.get("llm_type", "openai")
            model = model_config.get("model")
            # api_key = model_config.get("api_key")
            # base_url = model_config.get("base_url")

            # Validate required fields
            if not model:
                logger.error("Model name is required in model configuration")
                return None

            # Extract common optional parameters
            temperature = model_config.get("temperature")
            max_tokens = model_config.get("max_tokens")
            model_info_config = model_config.get("model_info", {})

            # Handle OpenAI models (including Requesty routing)
            if provider == "OpenAIChatCompletionClient":
                return ModelFactory._create_openai_client(
                    model,
                    llm_type,
                    temperature,
                    max_tokens,
                    model_info_config,
                )

            # Handle Anthropic models
            elif provider == "AnthropicChatCompletionClient":
                return ModelFactory._create_anthropic_client(
                    model, temperature, max_tokens, model_info_config
                )

            # Handle Ollama models
            elif provider == "OllamaChatCompletionClient":
                return ModelFactory._create_ollama_client(
                    model, temperature, max_tokens, model_info_config
                )

            else:
                # For unsupported providers, try to use OpenAI client with Requesty routing
                logger.info(
                    f"Provider '{provider}' not directly supported, attempting Requesty routing"
                )

                # Check if model is available through Requesty
                if ModelFactory.is_model_supported(model):
                    logger.info(
                        f"Model '{model}' found in Requesty, using OpenAI client for routing"
                    )
                    return ModelFactory._create_openai_client(
                        model,
                        llm_type,
                        temperature,
                        max_tokens,
                        model_info_config,
                    )
                else:
                    logger.warning(
                        f"Model '{model}' not found in Requesty either, cannot create client"
                    )
                    return None

        except Exception as e:
            logger.error(f"Error creating model client: {str(e)}")
            return None

    @staticmethod
    def _create_openai_client(
        model: str,
        llm_type: str,
        temperature: Optional[float],
        max_tokens: Optional[int],
        model_info_config: Dict[str, Any],
    ) -> Optional[OpenAIChatCompletionClient]:
        """Create OpenAI-compatible client (supports Requesty and OpenRouter routing)."""
        try:
            # Determine provider based on settings or explicit configuration
            provider = settings.model_provider.lower()

            if provider == "openrouter":
                api_key = settings.openrouter.api_key
                base_url = settings.openrouter.base_url
            elif provider == "openai":
                api_key = settings.openai.api_key
                base_url = "https://api.openai.com/v1"
            else:  # Default to requesty
                api_key = settings.requesty.api_key
                base_url = settings.requesty.base_url

            # Determine model family
            model_family = ModelFactory._determine_model_family(model, llm_type)

            # Format model based on provider
            if provider == "openrouter":
                # OpenRouter expects models in format like "openai/gpt-4o" or "anthropic/claude-3-sonnet"
                if "/" not in model:
                    formatted_model = f"{llm_type}/{model}"
                else:
                    formatted_model = model
            elif provider == "openai":
                # OpenAI expects just the model name
                formatted_model = model.split("/")[-1] if "/" in model else model
            else:  # Requesty
                # Requesty expects provider/model format
                formatted_model = f"{llm_type}/{model}" if "/" not in model else model

            # Create comprehensive model info
            model_info = ModelFactory._create_model_info(
                model, llm_type, model_info_config, model_family
            )

            # Create client with optional OpenRouter headers
            client_kwargs = {
                "model": formatted_model,
                "api_key": api_key,
                "base_url": base_url,
                "model_info": model_info,
                "temperature": temperature,
                "max_tokens": max_tokens,
            }

            # Add OpenRouter-specific headers if using OpenRouter
            if provider == "openrouter":
                extra_headers = {}
                if settings.openrouter.site_url:
                    extra_headers["HTTP-Referer"] = settings.openrouter.site_url
                if settings.openrouter.site_name:
                    extra_headers["X-Title"] = settings.openrouter.site_name

                if extra_headers:
                    # Note: OpenAIChatCompletionClient may not support extra_headers directly
                    # This would need to be handled at the HTTP client level
                    logger.info(f"OpenRouter headers would be: {extra_headers}")

            return OpenAIChatCompletionClient(**client_kwargs)
        except Exception as e:
            logger.error(f"Error creating OpenAI client: {str(e)}")
            return None

    @staticmethod
    def _create_anthropic_client(
        model: str,
        temperature: Optional[float],
        max_tokens: Optional[int],
        model_info_config: Dict[str, Any],
    ) -> Optional[AnthropicChatCompletionClient]:
        """Create Anthropic client."""
        try:
            # Use environment variable or Requesty key if not provided

            api_key = os.getenv("ANTHROPIC_API_KEY") or settings.requesty.api_key

            # Determine model family
            model_family = ModelFactory._determine_model_family(model, "anthropic")

            # Create model info
            model_info = ModelFactory._create_model_info(
                model, "anthropic", model_info_config, model_family
            )

            return AnthropicChatCompletionClient(
                model=model,
                api_key=api_key,
                model_info=model_info,
                temperature=temperature,
                max_tokens=max_tokens,
            )
        except Exception as e:
            logger.error(f"Error creating Anthropic client: {str(e)}")
            return None

    @staticmethod
    def _create_ollama_client(
        model: str,
        temperature: Optional[float],
        max_tokens: Optional[int],
        model_info_config: Dict[str, Any],
    ) -> Optional[Any]:
        """Create Ollama client for local models."""
        if not OllamaChatCompletionClient:
            logger.error(
                "Ollama client not available. Install with: pip install 'autogen-ext[ollama]'"
            )
            return None

        try:
            # Default Ollama base URL
            if not base_url:
                base_url = "http://localhost:11434"

            # Create model info with defaults suitable for local models
            model_info = {
                "vision": model_info_config.get("vision", False),
                "function_calling": model_info_config.get("function_calling", True),
                "json_output": model_info_config.get("json_output", True),
                "family": model_info_config.get("family", ModelFamily.UNKNOWN),
                "structured_output": model_info_config.get("structured_output", True),
                "multiple_system_messages": model_info_config.get(
                    "multiple_system_messages", True
                ),
            }

            return OllamaChatCompletionClient(
                model=model,
                base_url=base_url,
                model_info=model_info,
                temperature=temperature,
                max_tokens=max_tokens,
            )
        except Exception as e:
            logger.error(f"Error creating Ollama client: {str(e)}")
            return None

    @staticmethod
    def _create_model_info(
        model: str, llm_type: str, model_info_config: Dict[str, Any], model_family: str
    ) -> Dict[str, Any]:
        """Create model info with proper validation and Requesty capabilities."""
        # First try to get capabilities from Requesty API
        requesty_capabilities = ModelFactory.get_model_capabilities_from_requesty(model)

        # Merge with user-provided config, giving priority to user config
        model_info = {
            "vision": model_info_config.get(
                "vision",
                requesty_capabilities.get(
                    "vision", ModelFactory._supports_vision(model, llm_type)
                ),
            ),
            "function_calling": model_info_config.get(
                "function_calling", requesty_capabilities.get("function_calling", True)
            ),
            "json_output": model_info_config.get(
                "json_output", requesty_capabilities.get("json_output", True)
            ),
            "family": model_info_config.get("family", model_family),
            "structured_output": model_info_config.get(
                "structured_output",
                requesty_capabilities.get("structured_output", True),
            ),
            "multiple_system_messages": model_info_config.get(
                "multiple_system_messages",
                requesty_capabilities.get(
                    "multiple_system_messages",
                    ModelFactory._supports_multiple_system_messages(model, llm_type),
                ),
            ),
        }

        # Validate model info
        try:
            validate_model_info(model_info)
        except Exception as e:
            logger.warning(f"Model info validation failed: {e}")
            # Use safe defaults if validation fails
            model_info["multiple_system_messages"] = True

        return model_info

    @staticmethod
    def _determine_model_family(model: str, llm_type: str) -> str:
        """
        Determine the model family based on model name and type.
        Supports all major model providers and their model families.

        Args:
            model: The model name (can include provider prefix like "anthropic/claude-3-sonnet")
            llm_type: The LLM type (openai, anthropic, google, etc.)

        Returns:
            The model family string
        """
        # Handle Requesty format (provider/model)
        if "/" in model:
            provider_prefix, model_name = model.split("/", 1)
            model_lower = model_name.lower()
            # Override llm_type if provider prefix is present
            if provider_prefix in [
                "openai",
                "anthropic",
                "google",
                "gemini",
                "deepseek",
                "meta",
                "mistral",
            ]:
                llm_type = provider_prefix
        else:
            model_lower = model.lower()

        # OpenAI models
        if (
            llm_type == "openai"
            or "gpt" in model_lower
            or "o1" in model_lower
            or "o3" in model_lower
        ):
            if "gpt-4o" in model_lower:
                return ModelFamily.GPT_4O
            elif "gpt-4" in model_lower:
                if "gpt-41" in model_lower:
                    return ModelFamily.GPT_41
                elif "gpt-45" in model_lower:
                    return ModelFamily.GPT_45
                else:
                    return ModelFamily.GPT_4
            elif "gpt-3.5" in model_lower or "gpt-35" in model_lower:
                return ModelFamily.GPT_35
            elif "o1" in model_lower:
                return ModelFamily.O1
            elif "o3" in model_lower:
                return ModelFamily.O3
            elif "o4" in model_lower:
                return ModelFamily.O4
            elif "r1" in model_lower:
                return ModelFamily.R1

        # Anthropic models
        elif llm_type == "anthropic" or "claude" in model_lower:
            if "claude-3-5-sonnet" in model_lower:
                return ModelFamily.CLAUDE_3_5_SONNET
            elif "claude-3-5-haiku" in model_lower:
                return ModelFamily.CLAUDE_3_5_HAIKU
            elif "claude-3-7-sonnet" in model_lower:
                return ModelFamily.CLAUDE_3_7_SONNET
            elif "claude-3-opus" in model_lower:
                return ModelFamily.CLAUDE_3_OPUS
            elif "claude-3-sonnet" in model_lower:
                return ModelFamily.CLAUDE_3_SONNET
            elif "claude-3-haiku" in model_lower:
                return ModelFamily.CLAUDE_3_HAIKU
            elif "claude-4-opus" in model_lower:
                return ModelFamily.CLAUDE_4_OPUS
            elif "claude-4-sonnet" in model_lower:
                return ModelFamily.CLAUDE_4_SONNET

        # Google/Gemini models
        elif llm_type in ["google", "gemini"] or "gemini" in model_lower:
            if "gemini-1.5-flash" in model_lower:
                return ModelFamily.GEMINI_1_5_FLASH
            elif "gemini-1.5-pro" in model_lower:
                return ModelFamily.GEMINI_1_5_PRO
            elif "gemini-2.0-flash" in model_lower:
                return ModelFamily.GEMINI_2_0_FLASH
            elif "gemini-2.5-flash" in model_lower:
                return ModelFamily.GEMINI_2_5_FLASH
            elif "gemini-2.5-pro" in model_lower:
                return ModelFamily.GEMINI_2_5_PRO

        # DeepSeek models
        elif llm_type == "deepseek" or "deepseek" in model_lower:
            # DeepSeek models don't have specific families in AutoGen yet
            return ModelFamily.UNKNOWN

        # Meta/Llama models
        elif llm_type == "meta" or "llama" in model_lower:
            # Llama models don't have specific families in AutoGen yet
            return ModelFamily.UNKNOWN

        # Mistral models
        elif llm_type == "mistral" or "mistral" in model_lower:
            # Mistral models don't have specific families in AutoGen yet
            return ModelFamily.UNKNOWN

        # Default to unknown for any unrecognized models
        return ModelFamily.UNKNOWN

    @staticmethod
    def _supports_multiple_system_messages(model: str, llm_type: str) -> bool:
        """
        Determine if a model supports multiple system messages.
        Updated to handle Requesty format and more model types.

        Args:
            model: The model name (can include provider prefix)
            llm_type: The LLM type

        Returns:
            True if the model supports multiple system messages
        """
        # Handle Requesty format (provider/model)
        if "/" in model:
            provider_prefix, model_name = model.split("/", 1)
            model_lower = model_name.lower()
            if provider_prefix in [
                "openai",
                "anthropic",
                "google",
                "gemini",
                "deepseek",
                "meta",
                "mistral",
            ]:
                llm_type = provider_prefix
        else:
            model_lower = model.lower()

        # OpenAI models generally support multiple system messages
        if llm_type == "openai" or "gpt" in model_lower:
            # O1 and O3 models have restrictions on system messages
            if "o1" in model_lower or "o3" in model_lower:
                return False
            # Most other OpenAI models support multiple system messages
            return True

        # Anthropic models generally support multiple system messages
        elif llm_type == "anthropic" or "claude" in model_lower:
            return True

        # Google/Gemini models support multiple system messages
        elif llm_type in ["google", "gemini"] or "gemini" in model_lower:
            return True

        # DeepSeek models support multiple system messages
        elif llm_type == "deepseek" or "deepseek" in model_lower:
            return True

        # Meta/Llama models support multiple system messages
        elif llm_type == "meta" or "llama" in model_lower:
            return True

        # Mistral models support multiple system messages
        elif llm_type == "mistral" or "mistral" in model_lower:
            return True

        # Default to True for safety (allows multiple system messages)
        return True

    @staticmethod
    def _supports_vision(model: str, llm_type: str) -> bool:
        """
        Determine if a model supports vision capabilities.
        Updated to handle Requesty format and more model types.

        Args:
            model: The model name (can include provider prefix)
            llm_type: The LLM type

        Returns:
            True if the model supports vision
        """
        # Handle Requesty format (provider/model)
        if "/" in model:
            provider_prefix, model_name = model.split("/", 1)
            model_lower = model_name.lower()
            if provider_prefix in [
                "openai",
                "anthropic",
                "google",
                "gemini",
                "deepseek",
                "meta",
                "mistral",
            ]:
                llm_type = provider_prefix
        else:
            model_lower = model.lower()

        # OpenAI vision models
        if llm_type == "openai" or "gpt" in model_lower:
            return (
                "gpt-4o" in model_lower
                or "gpt-4-vision" in model_lower
                or "gpt-4-turbo" in model_lower
                or "gpt-4v" in model_lower
            )

        # Anthropic vision models
        elif llm_type == "anthropic" or "claude" in model_lower:
            # Claude 3 and later models support vision
            return "claude-3" in model_lower or "claude-4" in model_lower

        # Google/Gemini models support vision
        elif llm_type in ["google", "gemini"] or "gemini" in model_lower:
            return True

        # DeepSeek models - some support vision
        elif llm_type == "deepseek" or "deepseek" in model_lower:
            return "vision" in model_lower or "v" in model_lower

        # Meta/Llama models - some support vision
        elif llm_type == "meta" or "llama" in model_lower:
            return "vision" in model_lower or "v" in model_lower

        # Mistral models - some support vision
        elif llm_type == "mistral" or "mistral" in model_lower:
            return "vision" in model_lower or "v" in model_lower

        # Default to False for safety
        return False

    @staticmethod
    def get_supported_providers() -> Dict[str, Dict[str, Any]]:
        """
        Get information about all supported model providers.

        Returns:
            Dictionary with provider information and capabilities
        """
        return {
            "OpenAIChatCompletionClient": {
                "description": "OpenAI models and OpenAI-compatible APIs (including Requesty routing)",
                "supports_requesty": True,
                "example_models": [
                    "gpt-4o",
                    "gpt-4",
                    "gpt-3.5-turbo",
                    "anthropic/claude-3-sonnet",
                ],
                "required_config": ["model"],
                "optional_config": ["api_key", "base_url", "temperature", "max_tokens"],
            },
            "AnthropicChatCompletionClient": {
                "description": "Anthropic Claude models",
                "supports_requesty": False,
                "example_models": [
                    "claude-3-sonnet-********",
                    "claude-3-5-sonnet-********",
                ],
                "required_config": ["model"],
                "optional_config": ["api_key", "temperature", "max_tokens"],
            },
            "AzureOpenAIChatCompletionClient": {
                "description": "Azure OpenAI Service models",
                "supports_requesty": False,
                "example_models": ["gpt-4", "gpt-35-turbo"],
                "required_config": ["model", "azure_deployment", "azure_endpoint"],
                "optional_config": [
                    "api_key",
                    "api_version",
                    "temperature",
                    "max_tokens",
                ],
            },
            "AzureAIChatCompletionClient": {
                "description": "Azure AI Foundry and GitHub models",
                "supports_requesty": False,
                "example_models": ["Phi-4", "gpt-4o-mini"],
                "required_config": ["model"],
                "optional_config": [
                    "endpoint",
                    "credential",
                    "api_key",
                    "temperature",
                    "max_tokens",
                ],
            },
            "OllamaChatCompletionClient": {
                "description": "Local Ollama models",
                "supports_requesty": False,
                "example_models": ["llama3.2", "mistral", "codellama"],
                "required_config": ["model"],
                "optional_config": ["base_url", "temperature", "max_tokens"],
            },
        }

    @staticmethod
    async def fetch_requesty_models() -> List[Dict[str, Any]]:
        """
        Fetch all available models from Requesty API.

        Returns:
            List of model dictionaries with capabilities and pricing info
        """
        global _requesty_models_cache, _cache_timestamp

        # Check if cache is still valid
        if (
            _requesty_models_cache is not None
            and _cache_timestamp is not None
            and datetime.now() - _cache_timestamp < _cache_duration
        ):
            logger.debug("Using cached Requesty models")
            return _requesty_models_cache

        try:

            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE

            connector = aiohttp.TCPConnector(ssl=ssl_context)
            timeout = aiohttp.ClientTimeout(total=10)  # 10 second timeout

            async with aiohttp.ClientSession(
                connector=connector, timeout=timeout
            ) as session:
                async with session.get(
                    "https://api.requesty.ai/router/models"
                ) as response:
                    if response.status == 200:
                        models = await response.json()
                        _requesty_models_cache = models
                        _cache_timestamp = datetime.now()
                        logger.info(f"Fetched {len(models)} models from Requesty API")
                        return models
                    else:
                        logger.error(
                            f"Failed to fetch Requesty models: HTTP {response.status}"
                        )
                        return []
        except Exception as e:
            logger.warning(
                f"Could not fetch Requesty models (this is normal in some environments): {str(e)}"
            )
            return []

    @staticmethod
    def get_requesty_models_sync() -> List[Dict[str, Any]]:
        """
        Synchronous wrapper for fetching Requesty models.

        Returns:
            List of model dictionaries
        """
        try:
            # Try to get existing event loop
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If loop is running, we can't use run_until_complete
                # Return cached models if available, otherwise empty list
                if _requesty_models_cache is not None:
                    return _requesty_models_cache
                else:
                    logger.warning(
                        "Cannot fetch Requesty models in running event loop, returning empty list"
                    )
                    return []
            else:
                return loop.run_until_complete(ModelFactory.fetch_requesty_models())
        except RuntimeError:
            # No event loop, create a new one
            return asyncio.run(ModelFactory.fetch_requesty_models())

    @staticmethod
    def get_model_capabilities_from_requesty(model_name: str) -> Dict[str, Any]:
        """
        Get model capabilities from Requesty API data.

        Args:
            model_name: The model name to look up

        Returns:
            Dictionary with model capabilities
        """
        models = ModelFactory.get_requesty_models_sync()

        # Find the model in the Requesty data
        for model_data in models:
            if model_data.get("model") == model_name:
                return {
                    "vision": model_data.get("supports_vision", False),
                    "function_calling": True,  # Most models support function calling
                    "json_output": True,  # Most models support JSON output
                    "structured_output": True,
                    "multiple_system_messages": True,
                    "supports_caching": model_data.get("supports_caching", False),
                    "supports_reasoning": model_data.get("supports_reasoning", False),
                    "supports_computer_use": model_data.get(
                        "supports_computer_use", False
                    ),
                    "max_output_tokens": model_data.get("max_output_tokens"),
                    "context_window": model_data.get("context_window"),
                    "provider": model_data.get("provider"),
                    "description": model_data.get("description"),
                }

        # If model not found in Requesty data, return safe defaults
        return {
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_caching": False,
            "supports_reasoning": False,
            "supports_computer_use": False,
        }

    @staticmethod
    def get_all_available_models() -> Dict[str, List[str]]:
        """
        Get all available models organized by provider.

        Returns:
            Dictionary with provider names as keys and model lists as values
        """
        models_by_provider = {}
        requesty_models = ModelFactory.get_requesty_models_sync()

        for model_data in requesty_models:
            provider = model_data.get("provider", "unknown")
            model_name = model_data.get("model", "")

            if provider not in models_by_provider:
                models_by_provider[provider] = []

            if model_name and model_name not in models_by_provider[provider]:
                models_by_provider[provider].append(model_name)

        return models_by_provider

    @staticmethod
    def is_model_supported(model_name: str) -> bool:
        """
        Check if a model is supported through the current provider.

        Args:
            model_name: The model name to check

        Returns:
            True if the model is available through the current provider
        """
        provider = settings.model_provider.lower()

        if provider == "openrouter":
            models = ModelFactory.get_openrouter_models_sync()
            return any(model_data.get("id") == model_name for model_data in models)
        else:  # Default to requesty
            models = ModelFactory.get_requesty_models_sync()
            return any(model_data.get("model") == model_name for model_data in models)

    @staticmethod
    async def fetch_openrouter_models() -> List[Dict[str, Any]]:
        """
        Fetch all available models from OpenRouter API.

        Returns:
            List of model dictionaries with capabilities and pricing info
        """
        global _openrouter_models_cache, _openrouter_cache_timestamp

        # Check if cache is still valid
        if (
            _openrouter_models_cache is not None
            and _openrouter_cache_timestamp is not None
            and datetime.now() - _openrouter_cache_timestamp < _cache_duration
        ):
            logger.debug("Using cached OpenRouter models")
            return _openrouter_models_cache

        try:
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE

            connector = aiohttp.TCPConnector(ssl=ssl_context)
            timeout = aiohttp.ClientTimeout(total=10)  # 10 second timeout

            async with aiohttp.ClientSession(
                connector=connector, timeout=timeout
            ) as session:
                async with session.get(
                    "https://openrouter.ai/api/v1/models"
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        models = data.get("data", [])
                        _openrouter_models_cache = models
                        _openrouter_cache_timestamp = datetime.now()
                        logger.info(f"Fetched {len(models)} models from OpenRouter API")
                        return models
                    else:
                        logger.error(
                            f"Failed to fetch OpenRouter models: HTTP {response.status}"
                        )
                        return []
        except Exception as e:
            logger.warning(
                f"Could not fetch OpenRouter models (this is normal in some environments): {str(e)}"
            )
            return []

    @staticmethod
    def get_openrouter_models_sync() -> List[Dict[str, Any]]:
        """
        Synchronous wrapper for fetching OpenRouter models.

        Returns:
            List of model dictionaries
        """
        try:
            # Try to get existing event loop
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If loop is running, we can't use run_until_complete
                # Return cached models if available, otherwise empty list
                if _openrouter_models_cache is not None:
                    return _openrouter_models_cache
                else:
                    logger.warning(
                        "Cannot fetch OpenRouter models in running event loop, returning empty list"
                    )
                    return []
            else:
                return loop.run_until_complete(ModelFactory.fetch_openrouter_models())
        except RuntimeError:
            # No event loop, create a new one
            return asyncio.run(ModelFactory.fetch_openrouter_models())

    @staticmethod
    def get_model_capabilities_from_openrouter(model_name: str) -> Dict[str, Any]:
        """
        Get model capabilities from OpenRouter API data.

        Args:
            model_name: The model name to look up

        Returns:
            Dictionary with model capabilities
        """
        models = ModelFactory.get_openrouter_models_sync()

        # Find the model in the OpenRouter data
        for model_data in models:
            if model_data.get("id") == model_name:
                architecture = model_data.get("architecture", {})
                pricing = model_data.get("pricing", {})
                supported_params = model_data.get("supported_parameters", [])

                return {
                    "vision": "image" in architecture.get("input_modalities", []),
                    "function_calling": "tools" in supported_params,
                    "json_output": "response_format" in supported_params,
                    "structured_output": "structured_outputs" in supported_params,
                    "multiple_system_messages": True,  # Most models support this
                    "supports_caching": pricing.get("internal_reasoning", "0") != "0",
                    "supports_reasoning": "reasoning" in supported_params,
                    "supports_computer_use": False,  # Not commonly available
                    "max_output_tokens": None,  # Not provided in OpenRouter API
                    "context_window": model_data.get("context_length"),
                    "provider": (
                        model_data.get("id", "").split("/")[0]
                        if "/" in model_data.get("id", "")
                        else "unknown"
                    ),
                    "description": model_data.get("description"),
                }

        # If model not found in OpenRouter data, return safe defaults
        return {
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_caching": False,
            "supports_reasoning": False,
            "supports_computer_use": False,
        }
