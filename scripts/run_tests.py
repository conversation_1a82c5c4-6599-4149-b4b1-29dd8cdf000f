#!/usr/bin/env python3
"""
Test Runner Script for Agent Platform

This script provides comprehensive test execution and coverage reporting
for the Agent Platform project. It supports various test scenarios and
generates detailed coverage reports in multiple formats.

Usage:
    python scripts/run_tests.py [options]

Examples:
    python scripts/run_tests.py                    # Run all tests
    python scripts/run_tests.py --module kafka    # Run specific module
    python scripts/run_tests.py --no-coverage     # Run without coverage
    python scripts/run_tests.py --html-only       # HTML coverage report
    python scripts/run_tests.py --verbose         # Run with verbose output
"""

import argparse
import subprocess
import sys
from pathlib import Path
from typing import List


class TestRunner:
    """Comprehensive test runner with coverage reporting capabilities."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.coverage_dir = self.project_root / "coverage_html"
        self.coverage_file = self.project_root / ".coverage"
        
    def run_command(self, command: List[str], description: str) -> bool:
        """Execute a command and return success status."""
        print(f"\n{'='*60}")
        print(f"🚀 {description}")
        print(f"{'='*60}")
        print(f"Command: {' '.join(command)}")
        print()
        
        try:
            subprocess.run(
                command,
                cwd=self.project_root,
                check=True,
                text=True
            )
            print(f"\n✅ {description} completed successfully!")
            return True
        except subprocess.CalledProcessError as e:
            print(f"\n❌ {description} failed with exit code {e.returncode}")
            return False
        except FileNotFoundError:
            print(f"\n❌ Command not found: {command[0]}")
            print("Make sure all dependencies are installed (poetry install)")
            return False

    def clean_coverage_data(self):
        """Clean previous coverage data."""
        print("\n🧹 Cleaning previous coverage data...")
        
        # Remove coverage file
        if self.coverage_file.exists():
            self.coverage_file.unlink()
            print(f"   Removed: {self.coverage_file}")
        
        # Remove HTML coverage directory
        if self.coverage_dir.exists():
            import shutil
            shutil.rmtree(self.coverage_dir)
            print(f"   Removed: {self.coverage_dir}")

    def get_test_command(self, args) -> List[str]:
        """Build the poetry test command based on arguments."""
        cmd = ["poetry", "run", "python", "-m", "pytest"]
        
        # Add verbosity
        if args.verbose:
            cmd.append("-vv")
        else:
            cmd.append("-v")
        
        # Add coverage options
        if not args.no_coverage:
            cmd.extend([
                "--cov=app",
                "--cov-config=.coveragerc"
            ])
            
            # Coverage report formats
            if args.html_only:
                cmd.append("--cov-report=html")
            elif args.terminal_only:
                cmd.append("--cov-report=term-missing")
            else:
                # Default: both HTML and terminal
                cmd.extend([
                    "--cov-report=html",
                    "--cov-report=term-missing"
                ])
        
        # Add specific test paths
        if args.module:
            test_path = f"tests/{args.module}/"
            if not (self.project_root / test_path).exists():
                print(f"❌ Test module path does not exist: {test_path}")
                available_modules = self._get_available_test_modules()
                if available_modules:
                    modules_str = ', '.join(available_modules)
                    print(f"Available test modules: {modules_str}")
                sys.exit(1)
            cmd.append(test_path)
        elif args.test_file:
            if not (self.project_root / args.test_file).exists():
                print(f"❌ Test file does not exist: {args.test_file}")
                sys.exit(1)
            cmd.append(args.test_file)
        else:
            # Run all tests
            cmd.append("tests/")
        
        # Add additional pytest options
        if args.fail_fast:
            cmd.append("-x")
        
        if args.capture == "no":
            cmd.append("-s")
        
        return cmd

    def _get_available_test_modules(self) -> List[str]:
        """Get list of available test modules."""
        tests_dir = self.project_root / "tests"
        if not tests_dir.exists():
            return []
        
        modules = []
        for item in tests_dir.iterdir():
            if item.is_dir() and not item.name.startswith("__"):
                modules.append(item.name)
        return sorted(modules)

    def generate_coverage_reports(self, args):
        """Generate additional coverage reports if needed."""
        if args.no_coverage:
            return
        
        print(f"\n{'='*60}")
        print("📊 Generating Additional Coverage Reports")
        print(f"{'='*60}")
        
        # Generate XML report for CI/CD
        if args.xml_report:
            xml_cmd = ["poetry", "run", "python", "-m", "coverage", "xml"]
            self.run_command(xml_cmd, "Generating XML coverage report")
        
        # Generate JSON report
        if args.json_report:
            json_cmd = ["poetry", "run", "python", "-m", "coverage", "json"]
            self.run_command(json_cmd, "Generating JSON coverage report")
        
        # Show coverage summary
        summary_cmd = [
            "poetry", "run", "python", "-m", "coverage",
            "report", "--show-missing"
        ]
        self.run_command(summary_cmd, "Coverage Summary")

    def display_results(self, success: bool, args):
        """Display final results and helpful information."""
        print(f"\n{'='*60}")
        if success:
            print("🎉 TEST EXECUTION COMPLETED SUCCESSFULLY!")
        else:
            print("💥 TEST EXECUTION FAILED!")
        print(f"{'='*60}")
        
        if not args.no_coverage and success:
            print("\n📋 Coverage Reports Generated:")
            
            if not args.terminal_only:
                html_path = self.coverage_dir / "index.html"
                if html_path.exists():
                    print(f"   📄 HTML Report: {html_path}")
                    browser_url = f"file://{html_path.absolute()}"
                    print(f"   🌐 Open in browser: {browser_url}")
            
            if args.xml_report:
                xml_path = self.project_root / "coverage.xml"
                if xml_path.exists():
                    print(f"   📄 XML Report: {xml_path}")
            
            if args.json_report:
                json_path = self.project_root / "coverage.json"
                if json_path.exists():
                    print(f"   📄 JSON Report: {json_path}")
        
        print(f"\n📁 Project Root: {self.project_root}")
        print(f"📁 Test Directory: {self.project_root / 'tests'}")

    def run_tests(self, args):
        """Main test execution workflow."""
        print("🧪 Agent Platform Test Runner")
        print(f"Project: {self.project_root}")
        
        # Clean previous coverage data
        if not args.no_coverage and not args.keep_coverage:
            self.clean_coverage_data()
        
        # Build and run test command
        test_cmd = self.get_test_command(args)
        success = self.run_command(test_cmd, "Running Tests")
        
        # Generate additional reports
        if success:
            self.generate_coverage_reports(args)
        
        # Display results
        self.display_results(success, args)
        
        return success


def main():
    """Main entry point for the test runner."""
    parser = argparse.ArgumentParser(
        description="Comprehensive test runner for Agent Platform",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s                           # Run all tests with coverage
  %(prog)s --module kafka            # Run tests for kafka module
  %(prog)s --module autogen_service  # Run tests for autogen_service module
  %(prog)s --test-file tests/test_model_factory.py  # Run specific test file
  %(prog)s --no-coverage             # Run tests without coverage
  %(prog)s --html-only               # Generate only HTML coverage report
  %(prog)s --terminal-only           # Show coverage only in terminal
  %(prog)s --verbose --fail-fast     # Verbose output, stop on first failure
  %(prog)s --xml-report --json-report # Generate XML and JSON reports
        """
    )
    
    # Test selection options
    test_group = parser.add_mutually_exclusive_group()
    test_group.add_argument(
        "--module", "-m",
        help="Run tests for specific module (e.g., kafka, memory, helper)"
    )
    test_group.add_argument(
        "--test-file", "-f",
        help="Run specific test file (e.g., tests/test_model_factory.py)"
    )
    
    # Coverage options
    coverage_group = parser.add_argument_group("Coverage Options")
    coverage_group.add_argument(
        "--no-coverage",
        action="store_true",
        help="Run tests without coverage reporting"
    )
    coverage_group.add_argument(
        "--keep-coverage",
        action="store_true",
        help="Keep previous coverage data (don't clean)"
    )
    
    # Coverage report format options
    report_group = coverage_group.add_mutually_exclusive_group()
    report_group.add_argument(
        "--html-only",
        action="store_true",
        help="Generate only HTML coverage report"
    )
    report_group.add_argument(
        "--terminal-only",
        action="store_true",
        help="Show coverage only in terminal"
    )
    
    # Additional report formats
    coverage_group.add_argument(
        "--xml-report",
        action="store_true",
        help="Generate XML coverage report (useful for CI/CD)"
    )
    coverage_group.add_argument(
        "--json-report",
        action="store_true",
        help="Generate JSON coverage report"
    )
    
    # Test execution options
    exec_group = parser.add_argument_group("Execution Options")
    exec_group.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Verbose test output"
    )
    exec_group.add_argument(
        "--fail-fast", "-x",
        action="store_true",
        help="Stop on first test failure"
    )
    exec_group.add_argument(
        "--capture",
        choices=["yes", "no"],
        default="yes",
        help="Capture stdout/stderr (default: yes)"
    )
    
    args = parser.parse_args()
    
    # Create and run test runner
    runner = TestRunner()
    success = runner.run_tests(args)
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()