#!/bin/bash

# Test Runner Shell Script for Agent Platform
# This is a simple shell wrapper for the Python test runner

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "pyproject.toml" ]; then
    print_error "pyproject.toml not found. Please run this script from the project root."
    exit 1
fi

# Check if Poetry is installed
if ! command -v poetry &> /dev/null; then
    print_error "Poetry is not installed. Please install Poetry first."
    print_info "Visit: https://python-poetry.org/docs/#installation"
    exit 1
fi

# Check if dependencies are installed
if [ ! -d ".venv" ] && [ ! -f "poetry.lock" ]; then
    print_warning "Dependencies may not be installed. Running 'poetry install'..."
    poetry install
fi

print_info "Starting Agent Platform Test Runner..."

# Run the Python test runner with all arguments passed through
python scripts/run_tests.py "$@"

# Capture the exit code
exit_code=$?

if [ $exit_code -eq 0 ]; then
    print_success "Test execution completed successfully!"
else
    print_error "Test execution failed!"
fi

exit $exit_code