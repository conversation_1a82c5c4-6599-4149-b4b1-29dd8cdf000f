#!/usr/bin/env python3
"""
Poetry-based Test Runner for Agent Platform

Simple test runner that uses Poetry commands directly for running tests
and generating coverage reports.

Usage:
    poetry run python scripts/test.py [options]

Examples:
    poetry run python scripts/test.py                    # Run all tests
    poetry run python scripts/test.py --module kafka    # Run specific module
    poetry run python scripts/test.py --coverage        # Run with coverage
    poetry run python scripts/test.py --html            # Generate HTML report
"""

import argparse
import subprocess
import sys
from pathlib import Path


def run_command(command, description="Running command"):
    """Execute a command and return success status."""
    print(f"\n🚀 {description}")
    print(f"Command: {' '.join(command)}")
    print("-" * 60)
    
    try:
        subprocess.run(command, check=True)
        print(f"\n✅ {description} completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"\n❌ {description} failed with exit code {e.returncode}")
        return False
    except FileNotFoundError:
        print(f"\n❌ Command not found: {command[0]}")
        return False


def main():
    parser = argparse.ArgumentParser(
        description="Poetry-based test runner for Agent Platform"
    )
    
    # Test selection
    parser.add_argument(
        "--module", "-m",
        help="Run tests for specific module (e.g., kafka_client, memory)"
    )
    parser.add_argument(
        "--file", "-f",
        help="Run specific test file"
    )
    
    # Coverage options
    parser.add_argument(
        "--coverage", "-c",
        action="store_true",
        help="Run tests with coverage"
    )
    parser.add_argument(
        "--html",
        action="store_true",
        help="Generate HTML coverage report"
    )
    parser.add_argument(
        "--xml",
        action="store_true",
        help="Generate XML coverage report"
    )
    
    # Test options
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Verbose output"
    )
    parser.add_argument(
        "--fail-fast", "-x",
        action="store_true",
        help="Stop on first failure"
    )
    
    args = parser.parse_args()
    
    # Build pytest command
    cmd = ["poetry", "run", "pytest"]
    
    # Add verbosity
    if args.verbose:
        cmd.append("-v")
    
    # Add fail-fast
    if args.fail_fast:
        cmd.append("-x")
    
    # Add coverage
    if args.coverage or args.html or args.xml:
        cmd.extend(["--cov=app", "--cov-config=.coveragerc"])
        
        if args.html:
            cmd.append("--cov-report=html")
        if args.xml:
            cmd.append("--cov-report=xml")
        if not args.html and not args.xml:
            cmd.append("--cov-report=term-missing")
    
    # Add test path
    if args.module:
        test_path = f"tests/{args.module}/"
        if not Path(test_path).exists():
            print(f"❌ Test module path does not exist: {test_path}")
            available = [d.name for d in Path("tests").iterdir()
                         if d.is_dir() and not d.name.startswith("__")]
            if available:
                print(f"Available modules: {', '.join(available)}")
            sys.exit(1)
        cmd.append(test_path)
    elif args.file:
        if not Path(args.file).exists():
            print(f"❌ Test file does not exist: {args.file}")
            sys.exit(1)
        cmd.append(args.file)
    else:
        cmd.append("tests/")
    
    # Run tests
    success = run_command(cmd, "Running Tests")
    
    # Generate additional reports if needed
    if success and (args.html or args.xml):
        print("\n📊 Coverage Reports Generated:")
        if args.html:
            html_path = Path("coverage_html/index.html")
            if html_path.exists():
                print(f"   📄 HTML Report: {html_path}")
                print(f"   🌐 Open: file://{html_path.absolute()}")
        if args.xml:
            xml_path = Path("coverage.xml")
            if xml_path.exists():
                print(f"   📄 XML Report: {xml_path}")
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()