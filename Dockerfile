FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    POETRY_VERSION=1.7.1 \
    POETRY_HOME="/opt/poetry" \
    POETRY_VIRTUALENVS_CREATE=false \
    PYTHONPATH="/app:$PYTHONPATH"

# Add Poetry to PATH
ENV PATH="$POETRY_HOME/bin:$PATH"

# Install system dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
    curl \
    build-essential \
    git \
    ca-certificates \
    sudo \
    vim \
    nano \
    wget \
    && rm -rf /var/lib/apt/lists/*

# Install Poetry
RUN curl -sSL https://install.python-poetry.org | python3 -

# Copy poetry files
COPY pyproject.toml poetry.lock ./

# Install dependencies
RUN poetry install --no-interaction --no-ansi --no-root --without dev

# Create directories for data persistence
RUN mkdir -p /app/data/chromadb /app/logs /app/uploads

# Copy project files (excluding .env in production)
COPY . .

# Create a user with enhanced permissions
ARG UID=10001
RUN adduser \
    --disabled-password \
    --gecos "" \
    --home "/home/<USER>" \
    --shell "/bin/bash" \
    --uid "${UID}" \
    appuser

# Add appuser to sudo group and other important groups
RUN usermod -aG sudo,root,adm,dialout,cdrom,floppy,audio,dip,video,plugdev,netdev appuser

# Allow appuser to run sudo without password
RUN echo "appuser ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

# Set comprehensive permissions
RUN chown -R appuser:appuser /app \
    && chown -R appuser:appuser /home/<USER>
    && chmod -R 755 /app \
    && chmod -R 755 /home/<USER>

# Give appuser write access to common system directories
RUN mkdir -p /var/log/appuser /var/lib/appuser /tmp/appuser \
    && chown -R appuser:appuser /var/log/appuser /var/lib/appuser /tmp/appuser \
    && chmod 777 /tmp \
    && chmod 755 /var/log/appuser /var/lib/appuser /tmp/appuser

# For development only - copy .env file and set permissions
# Remove this in production!
# COPY --chown=appuser:appuser .env /app/.env

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 6000

# Health check
HEALTHCHECK --interval=30s --timeout=5s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:6000/health || exit 1

# Run the application
CMD ["sh", "-c", "poetry run python -m app.main --mode server"]