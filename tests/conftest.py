"""
Enhanced pytest configuration and fixtures for comprehensive testing.

This module provides shared fixtures, test utilities, and configuration
for the entire test suite, including mocks for external dependencies.
"""

import asyncio
import pytest
from unittest.mock import AsyncMock, Mock, patch
from typing import Dict, Any, List

# Optional FastAPI imports - only import if available
try:
    from fastapi.testclient import TestClient
    from app.main import app
    FASTAPI_AVAILABLE = True
except ImportError:
    TestClient = None
    app = None
    FASTAPI_AVAILABLE = False

# Import application components
from app.shared.config.base import get_settings
from app.autogen_service.head_agent import HeadAgent
from app.tools.tool_loader import ToolLoader
from app.helper.redis_client import RedisClient
from app.helper.session_manager import SessionManager
from app.autogen_service.agent_factory import AgentFactory
from app.autogen_service.chat_processor import ChatProcessor
from app.kafka_client.producer import KafkaProducer
from app.kafka_client.consumer import KafkaConsumer
from app.memory.pinecone_memory import PineconeMemory
from app.schemas.agent_config import AgentConfig


# Test configuration
pytest_plugins = ['pytest_asyncio']


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def test_client():
    """Create FastAPI test client."""
    if not FASTAPI_AVAILABLE:
        pytest.skip("FastAPI not available")
    return TestClient(app)


@pytest.fixture
def settings():
    """Get application settings."""
    return get_settings()


@pytest.fixture
def tool_loader():
    """Create ToolLoader instance."""
    return ToolLoader()


@pytest.fixture
def head_agent():
    """Create HeadAgent instance."""
    return HeadAgent()


# Mock fixtures for external dependencies
@pytest.fixture
def mock_redis():
    """Create mock Redis client."""
    mock_redis = AsyncMock()
    mock_redis.ping.return_value = True
    mock_redis.get.return_value = None
    mock_redis.set.return_value = True
    mock_redis.delete.return_value = 1
    mock_redis.exists.return_value = False
    mock_redis.hgetall.return_value = {}
    mock_redis.hset.return_value = 1
    mock_redis.expire.return_value = True
    mock_redis.flushdb.return_value = True
    mock_redis.close.return_value = None
    return mock_redis


@pytest.fixture
def mock_pinecone():
    """Create mock Pinecone client."""
    mock_pinecone = Mock()
    mock_index = Mock()
    mock_pinecone.Index.return_value = mock_index
    mock_pinecone.list_indexes.return_value = []
    mock_pinecone.create_index.return_value = None
    
    # Mock index operations
    mock_index.upsert.return_value = None
    mock_index.query.return_value = Mock(matches=[])
    mock_index.delete.return_value = None
    mock_index.describe_index_stats.return_value = {
        "dimension": 1536,
        "index_fullness": 0.0,
        "namespaces": {}
    }
    
    return mock_pinecone, mock_index


@pytest.fixture
def mock_kafka_producer():
    """Create mock Kafka producer."""
    mock_producer = AsyncMock()
    mock_producer.start.return_value = None
    mock_producer.stop.return_value = None
    mock_producer.send_and_wait.return_value = None
    return mock_producer


@pytest.fixture
def mock_kafka_consumer():
    """Create mock Kafka consumer."""
    mock_consumer = AsyncMock()
    mock_consumer.start.return_value = None
    mock_consumer.stop.return_value = None
    mock_consumer.__aiter__.return_value = iter([])
    return mock_consumer


@pytest.fixture
def mock_openai():
    """Create mock OpenAI client."""
    mock_openai = Mock()
    mock_response = Mock()
    mock_response.data = [Mock(embedding=[0.1] * 1536)]
    mock_openai.embeddings.create.return_value = mock_response
    return mock_openai


# Redis client fixtures
@pytest.fixture
async def redis_client(mock_redis):
    """Create RedisClient with mocked Redis."""
    client = RedisClient()
    with patch.object(client, 'redis', mock_redis):
        yield client


@pytest.fixture
async def session_manager(redis_client):
    """Create SessionManager with mocked Redis."""
    return SessionManager(redis_client)


# Agent and factory fixtures
@pytest.fixture
def sample_agent_config():
    """Create sample AgentConfig for testing."""
    return AgentConfig(
        name="test_agent",
        description="A test agent for unit testing",
        system_message="You are a helpful test assistant.",
        model_provider="openai",
        model_name="gpt-4o-mini",
        tools=[],
        workflow_ids=[],
        mcp_server_ids=[],
        agent_category="general",
        visibility="private",
        tags={"test": True}
    )


@pytest.fixture
async def agent_factory(session_manager):
    """Create AgentFactory with mocked dependencies."""
    return AgentFactory()


@pytest.fixture
async def chat_processor(session_manager, agent_factory):
    """Create ChatProcessor with mocked dependencies."""
    return ChatProcessor(session_manager, agent_factory)


# Kafka fixtures
@pytest.fixture
def kafka_producer():
    """Create KafkaProducer instance."""
    return KafkaProducer()


@pytest.fixture
def kafka_consumer():
    """Create KafkaConsumer instance."""
    return KafkaConsumer()


# Memory fixtures
@pytest.fixture
def pinecone_memory_config():
    """Create Pinecone memory configuration."""
    return {
        "index_name": "test-index",
        "namespace": "test-namespace",
        "dimension": 1536,
        "metric": "cosine"
    }


@pytest.fixture
def pinecone_memory(pinecone_memory_config, mock_pinecone):
    """Create PineconeMemory with mocked Pinecone."""
    mock_pinecone_client, mock_index = mock_pinecone
    with patch(
        'app.memory.pinecone_memory.Pinecone',
        return_value=mock_pinecone_client
    ):
        memory = PineconeMemory(
            agent_id="test_agent",
            **pinecone_memory_config
        )
        yield memory


# Test data fixtures
@pytest.fixture
def sample_chat_message():
    """Create sample chat message."""
    return {
        "session_id": "test_session_123",
        "user_message": "Hello, how can you help me?",
        "user_id": "test_user_456"
    }


@pytest.fixture
def sample_agent_creation_message():
    """Create sample agent creation message."""
    return {
        "user_id": "test_user",
        "agent_config": {
            "name": "test_agent",
            "description": "Test agent for unit testing",
            "system_prompt": "You are a helpful test assistant",
            "model": "gpt-4o-mini",
            "tools": []
        },
        "session_id": "test_session"
    }


@pytest.fixture
def sample_memory_content():
    """Create sample memory content."""
    from app.memory.pinecone_memory import MemoryContent
    return MemoryContent(
        content="This is a test memory for unit testing",
        metadata={
            "type": "conversation",
            "timestamp": "2024-01-01T12:00:00Z",
            "source": "test"
        }
    )


# Utility fixtures
@pytest.fixture
def mock_http_response():
    """Create mock HTTP response."""
    mock_response = Mock()
    mock_response.status_code = 200
    mock_response.json.return_value = {"status": "success"}
    mock_response.text = "Success"
    return mock_response


@pytest.fixture
def mock_async_context_manager():
    """Create mock async context manager."""
    class MockAsyncContextManager:
        async def __aenter__(self):
            return self
        
        async def __aexit__(self, exc_type, exc_val, exc_tb):
            pass
    
    return MockAsyncContextManager()


# Test utilities
class TestUtils:
    """Utility class for common test operations."""
    
    @staticmethod
    def create_mock_consumer_record(
        topic: str, value: Dict[str, Any], key: str = None
    ):
        """Create mock Kafka consumer record."""
        import json
        from unittest.mock import Mock
        
        record = Mock()
        record.topic = topic
        record.partition = 0
        record.offset = 123
        record.key = key.encode() if key else None
        record.value = json.dumps(value).encode()
        record.headers = []
        return record
    
    @staticmethod
    def create_mock_chat_context(messages: List[str] = None):
        """Create mock chat completion context."""
        from unittest.mock import Mock
        from autogen_agentchat.messages import ChatMessage
        
        if messages is None:
            messages = ["Hello", "Hi there!"]
        
        context = Mock()
        context.messages = [
            ChatMessage(
                content=msg,
                source="user" if i % 2 == 0 else "assistant"
            )
            for i, msg in enumerate(messages)
        ]
        return context
    
    @staticmethod
    async def wait_for_async_operation(coro, timeout: float = 1.0):
        """Wait for async operation with timeout."""
        try:
            return await asyncio.wait_for(coro, timeout=timeout)
        except asyncio.TimeoutError:
            pytest.fail(f"Async operation timed out after {timeout} seconds")


@pytest.fixture
def test_utils():
    """Provide test utilities."""
    return TestUtils


# Async test markers
pytestmark = pytest.mark.asyncio


# Test configuration hooks
def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "integration: mark test as integration test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
    config.addinivalue_line(
        "markers", "external: mark test as requiring external services"
    )


def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers based on test names."""
    for item in items:
        # Mark integration tests
        if "integration" in item.nodeid:
            item.add_marker(pytest.mark.integration)
        
        # Mark slow tests
        if any(
            keyword in item.name
            for keyword in ["slow", "performance", "load"]
        ):
            item.add_marker(pytest.mark.slow)
        
        # Mark external dependency tests
        if any(
            keyword in item.name
            for keyword in ["kafka", "redis", "pinecone", "openai"]
        ):
            item.add_marker(pytest.mark.external)


# Cleanup fixtures
@pytest.fixture(autouse=True)
async def cleanup_after_test():
    """Cleanup after each test."""
    yield
    # Perform any necessary cleanup
    # Close any open connections, clear caches, etc.
    pass


# Environment setup
@pytest.fixture(scope="session", autouse=True)
def setup_test_environment():
    """Setup test environment before running tests."""
    # Set test environment variables
    import os
    os.environ["TESTING"] = "true"
    os.environ["LOG_LEVEL"] = "DEBUG"
    
    yield
    
    # Cleanup after all tests
    if "TESTING" in os.environ:
        del os.environ["TESTING"]
