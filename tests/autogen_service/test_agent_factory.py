"""
Unit tests for AgentFactory class.

This module contains comprehensive unit tests for the AgentFactory class,
covering agent creation, memory setup, and chat processing functionality.
"""

import pytest
from unittest.mock import AsyncMock, Mock, patch

from app.autogen_service.agent_factory import AgentFactory
from app.schemas.api import AgentConfig, ChatType
from app.helper.session_manager import SessionManager


class TestAgentFactory:
    """Test class for AgentFactory."""

    @pytest.fixture
    def agent_factory(self):
        """Create AgentFactory instance for testing."""
        return AgentFactory()

    @pytest.fixture
    def mock_session_manager(self):
        """Create mock SessionManager for testing."""
        mock_manager = AsyncMock(spec=SessionManager)
        mock_manager.create_session.return_value = "test_session_123"
        mock_manager.get_session_data.return_value = {
            "agent_config": {"name": "test_agent"},
            "communication_type": "single",
            "memory": [],
            "cancellation_token": None
        }
        return mock_manager

    @pytest.fixture
    def sample_agent_config(self):
        """Create sample AgentConfig for testing."""
        return AgentConfig(
            id="test_agent_id",
            name="test_agent",
            description="Test agent for unit testing",
            system_message="You are a helpful test assistant.",
            model_name="gpt-4o-mini",
            workflow_ids=[],
            mcp_server_ids=[]
        )

    def test_init(self, agent_factory):
        """Test AgentFactory initialization."""
        assert agent_factory is not None
        assert hasattr(agent_factory, 'logger')

    @pytest.mark.asyncio
    async def test_initialize_chat_session_single(
        self, agent_factory, mock_session_manager, sample_agent_config
    ):
        """Test initializing single agent chat session."""
        agent_configs = [sample_agent_config]
        chat_type = ChatType.SINGLE
        
        with patch.object(
            agent_factory, '_create_session_manager', 
            return_value=mock_session_manager
        ):
            session_id = await agent_factory.initialize_chat_session(
                agent_configs, chat_type
            )
            
            assert session_id == "test_session_123"
            mock_session_manager.create_session.assert_called_once()

    @pytest.mark.asyncio
    async def test_initialize_chat_session_group(
        self, agent_factory, mock_session_manager, sample_agent_config
    ):
        """Test initializing group chat session."""
        agent_configs = [sample_agent_config, sample_agent_config]
        chat_type = ChatType.GROUP
        
        with patch.object(
            agent_factory, '_create_session_manager', 
            return_value=mock_session_manager
        ):
            session_id = await agent_factory.initialize_chat_session(
                agent_configs, chat_type
            )
            
            assert session_id == "test_session_123"
            mock_session_manager.create_session.assert_called_once()

    @pytest.mark.asyncio
    async def test_initialize_chat_session_failure(
        self, agent_factory, mock_session_manager, sample_agent_config
    ):
        """Test chat session initialization failure."""
        agent_configs = [sample_agent_config]
        chat_type = ChatType.SINGLE
        
        mock_session_manager.create_session.side_effect = Exception(
            "Session creation failed"
        )
        
        with patch.object(
            agent_factory, '_create_session_manager', 
            return_value=mock_session_manager
        ):
            session_id = await agent_factory.initialize_chat_session(
                agent_configs, chat_type
            )
            
            assert session_id is None

    @pytest.mark.asyncio
    async def test_create_direct_agent_success(
        self, agent_factory, sample_agent_config
    ):
        """Test successful direct agent creation."""
        with patch(
            'app.autogen_service.agent_factory.AssistantAgent'
        ) as mock_agent_class:
            mock_agent = Mock()
            mock_agent_class.return_value = mock_agent
            
            with patch.object(
                agent_factory, '_get_or_create_model_client'
            ) as mock_model_client:
                mock_model_client.return_value = Mock()
                
                agent = await agent_factory.create_direct_agent(
                    sample_agent_config
                )
                
                assert agent == mock_agent
                mock_agent_class.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_direct_agent_no_model_client(
        self, agent_factory, sample_agent_config
    ):
        """Test direct agent creation when model client creation fails."""
        with patch.object(
            agent_factory, '_get_or_create_model_client', return_value=None
        ):
            agent = await agent_factory.create_direct_agent(
                sample_agent_config
            )
            
            assert agent is None

    @pytest.mark.asyncio
    async def test_create_direct_agent_exception(
        self, agent_factory, sample_agent_config
    ):
        """Test direct agent creation with exception."""
        with patch.object(
            agent_factory, '_get_or_create_model_client'
        ) as mock_model_client:
            mock_model_client.side_effect = Exception("Model client error")
            
            agent = await agent_factory.create_direct_agent(
                sample_agent_config
            )
            
            assert agent is None

    @pytest.mark.asyncio
    async def test_create_agent_success(
        self, agent_factory, sample_agent_config
    ):
        """Test successful agent creation with memory."""
        session_id = "test_session"
        
        with patch(
            'app.autogen_service.agent_factory.AssistantAgent'
        ) as mock_agent_class:
            mock_agent = Mock()
            mock_agent_class.return_value = mock_agent
            
            with patch.object(
                agent_factory, '_get_or_create_model_client'
            ) as mock_model_client:
                mock_model_client.return_value = Mock()
                
                with patch.object(
                    agent_factory, 'setup_agent_memory'
                ) as mock_setup_memory:
                    mock_setup_memory.return_value = None
                    
                    agent = await agent_factory.create_agent(
                        sample_agent_config, session_id
                    )
                    
                    assert agent == mock_agent
                    mock_setup_memory.assert_called_once()

    @pytest.mark.asyncio
    async def test_setup_agent_memory_success(
        self, agent_factory, sample_agent_config
    ):
        """Test successful agent memory setup."""
        mock_agent = Mock()
        session_id = "test_session"
        memory_data = [
            {"content": "Hello", "sender": "user"},
            {"content": "Hi there!", "sender": "agent"}
        ]
        
        with patch(
            'app.autogen_service.agent_factory.PineconeMemory'
        ) as mock_memory_class:
            mock_memory = AsyncMock()
            mock_memory_class.return_value = mock_memory
            
            await agent_factory.setup_agent_memory(
                mock_agent, sample_agent_config, session_id, memory_data
            )
            
            # Should create memory instance
            mock_memory_class.assert_called_once()

    @pytest.mark.asyncio
    async def test_setup_agent_memory_no_memory_data(
        self, agent_factory, sample_agent_config
    ):
        """Test agent memory setup with no memory data."""
        mock_agent = Mock()
        session_id = "test_session"
        memory_data = []
        
        with patch(
            'app.autogen_service.agent_factory.PineconeMemory'
        ) as mock_memory_class:
            await agent_factory.setup_agent_memory(
                mock_agent, sample_agent_config, session_id, memory_data
            )
            
            # Should still create memory instance
            mock_memory_class.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_or_create_model_client_success(self, agent_factory):
        """Test successful model client creation."""
        model_config = {
            "provider": "OpenAIChatCompletionClient",
            "model": "gpt-4o-mini",
            "temperature": 0.7
        }
        
        with patch(
            'app.autogen_service.agent_factory.ModelFactory'
        ) as mock_factory:
            mock_client = Mock()
            mock_factory.create_model_client.return_value = mock_client
            
            client = await agent_factory._get_or_create_model_client(
                model_config
            )
            
            assert client == mock_client
            mock_factory.create_model_client.assert_called_once_with(
                model_config
            )

    @pytest.mark.asyncio
    async def test_get_or_create_model_client_failure(self, agent_factory):
        """Test model client creation failure."""
        model_config = {
            "provider": "InvalidProvider",
            "model": "invalid-model"
        }
        
        with patch(
            'app.autogen_service.agent_factory.ModelFactory'
        ) as mock_factory:
            mock_factory.create_model_client.return_value = None
            
            client = await agent_factory._get_or_create_model_client(
                model_config
            )
            
            assert client is None

    @pytest.mark.asyncio
    async def test_load_workflow_tools_success(self, agent_factory):
        """Test successful workflow tools loading."""
        workflow_ids = ["workflow-1", "workflow-2"]
        
        with patch(
            'app.autogen_service.agent_factory.WorkflowToolLoader'
        ) as mock_loader:
            mock_tool_loader = Mock()
            mock_loader.return_value = mock_tool_loader
            mock_tool_loader.load_tools.return_value = [Mock(), Mock()]
            
            tools = await agent_factory._load_workflow_tools(workflow_ids)
            
            assert len(tools) == 2
            mock_tool_loader.load_tools.assert_called_once()

    @pytest.mark.asyncio
    async def test_load_workflow_tools_empty(self, agent_factory):
        """Test workflow tools loading with empty list."""
        workflow_ids = []
        
        tools = await agent_factory._load_workflow_tools(workflow_ids)
        
        assert tools == []

    @pytest.mark.asyncio
    async def test_load_mcp_tools_success(self, agent_factory):
        """Test successful MCP tools loading."""
        mcp_server_ids = ["mcp-1", "mcp-2"]
        
        with patch(
            'app.autogen_service.agent_factory.get_mcp_tools_for_agent'
        ) as mock_get_tools:
            mock_tools = [Mock(), Mock()]
            mock_get_tools.return_value = mock_tools
            
            tools = await agent_factory._load_mcp_tools(mcp_server_ids)
            
            assert len(tools) == 2
            mock_get_tools.assert_called_once()

    @pytest.mark.asyncio
    async def test_load_mcp_tools_empty(self, agent_factory):
        """Test MCP tools loading with empty list."""
        mcp_server_ids = []
        
        tools = await agent_factory._load_mcp_tools(mcp_server_ids)
        
        assert tools == []

    @pytest.mark.asyncio
    async def test_setup_autogen_agents_success(
        self, agent_factory, sample_agent_config
    ):
        """Test successful AutoGen agents setup."""
        agent_configs = [sample_agent_config]
        session_id = "test_session"
        
        with patch.object(
            agent_factory, 'create_agent'
        ) as mock_create_agent:
            mock_agent = Mock()
            mock_create_agent.return_value = mock_agent
            
            agents = await agent_factory.setup_autogen_agents(
                agent_configs, session_id
            )
            
            assert len(agents) == 1
            assert agents[0] == mock_agent
            mock_create_agent.assert_called_once()

    @pytest.mark.asyncio
    async def test_setup_autogen_agents_with_failures(
        self, agent_factory, sample_agent_config
    ):
        """Test AutoGen agents setup with some failures."""
        agent_configs = [sample_agent_config, sample_agent_config]
        session_id = "test_session"
        
        with patch.object(
            agent_factory, 'create_agent'
        ) as mock_create_agent:
            # First agent succeeds, second fails
            mock_agent = Mock()
            mock_create_agent.side_effect = [mock_agent, None]
            
            agents = await agent_factory.setup_autogen_agents(
                agent_configs, session_id
            )
            
            # Should only return successful agents
            assert len(agents) == 1
            assert agents[0] == mock_agent

    @pytest.mark.asyncio
    async def test_create_chat_team_success(
        self, agent_factory, sample_agent_config
    ):
        """Test successful chat team creation."""
        agent_configs = [sample_agent_config]
        session_id = "test_session"
        
        with patch.object(
            agent_factory, 'setup_autogen_agents'
        ) as mock_setup_agents:
            mock_agents = [Mock()]
            mock_setup_agents.return_value = mock_agents
            
            with patch(
                'app.autogen_service.agent_factory.RoundRobinGroupChat'
            ) as mock_team:
                mock_chat_team = Mock()
                mock_team.return_value = mock_chat_team
                
                team = await agent_factory._create_chat_team(
                    agent_configs, session_id
                )
                
                assert team == mock_chat_team
                mock_setup_agents.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_chat_team_no_agents(
        self, agent_factory, sample_agent_config
    ):
        """Test chat team creation when no agents are created."""
        agent_configs = [sample_agent_config]
        session_id = "test_session"
        
        with patch.object(
            agent_factory, 'setup_autogen_agents', return_value=[]
        ):
            team = await agent_factory._create_chat_team(
                agent_configs, session_id
            )
            
            assert team is None

    @pytest.mark.asyncio
    async def test_process_with_agents_success(
        self, agent_factory, sample_agent_config
    ):
        """Test successful message processing with agents."""
        user_message = "Hello, agents!"
        agent_configs = [sample_agent_config]
        session_id = "test_session"
        
        with patch.object(
            agent_factory, '_create_chat_team'
        ) as mock_create_team:
            mock_team = AsyncMock()
            mock_team.run.return_value = Mock(messages=[
                Mock(content="Agent response", source="assistant")
            ])
            mock_create_team.return_value = mock_team
            
            response = await agent_factory.process_with_agents(
                user_message=user_message,
                agent_configs=agent_configs,
                session_id=session_id
            )
            
            assert response is not None
            mock_team.run.assert_called_once()

    @pytest.mark.asyncio
    async def test_process_with_agents_no_team(
        self, agent_factory, sample_agent_config
    ):
        """Test message processing when team creation fails."""
        user_message = "Hello, agents!"
        agent_configs = [sample_agent_config]
        session_id = "test_session"
        
        with patch.object(
            agent_factory, '_create_chat_team', return_value=None
        ):
            response = await agent_factory.process_with_agents(
                user_message=user_message,
                agent_configs=agent_configs,
                session_id=session_id
            )
            
            assert response is None

    @pytest.mark.asyncio
    async def test_process_with_agents_exception(
        self, agent_factory, sample_agent_config
    ):
        """Test message processing with exception."""
        user_message = "Hello, agents!"
        agent_configs = [sample_agent_config]
        session_id = "test_session"
        
        with patch.object(
            agent_factory, '_create_chat_team'
        ) as mock_create_team:
            mock_create_team.side_effect = Exception("Team creation error")
            
            response = await agent_factory.process_with_agents(
                user_message=user_message,
                agent_configs=agent_configs,
                session_id=session_id
            )
            
            assert response is None

    @pytest.mark.asyncio
    async def test_load_knowledge_tools_success(self, agent_factory):
        """Test successful knowledge tools loading."""
        organization_id = "org-123"
        
        with patch(
            'app.autogen_service.agent_factory.KnowledgeToolLoader'
        ) as mock_loader:
            mock_tool_loader = Mock()
            mock_loader.return_value = mock_tool_loader
            mock_tool_loader.load_tools.return_value = [Mock()]
            
            tools = await agent_factory._load_knowledge_tools(
                use_knowledge=True,
                organization_id=organization_id
            )
            
            assert len(tools) == 1
            mock_tool_loader.load_tools.assert_called_once()

    @pytest.mark.asyncio
    async def test_load_knowledge_tools_disabled(self, agent_factory):
        """Test knowledge tools loading when disabled."""
        tools = await agent_factory._load_knowledge_tools(
            use_knowledge=False,
            organization_id="org-123"
        )
        
        assert tools == []

    @pytest.mark.asyncio
    async def test_load_knowledge_tools_no_org_id(self, agent_factory):
        """Test knowledge tools loading without organization ID."""
        tools = await agent_factory._load_knowledge_tools(
            use_knowledge=True,
            organization_id=None
        )
        
        assert tools == []

    def test_process_variables_success(self, agent_factory):
        """Test successful variable processing."""
        text = "Hello {{name}}, your score is {{score}}!"
        variables = {"name": "Alice", "score": 95}
        
        result = agent_factory.process_variables(text, variables)
        
        assert result == "Hello Alice, your score is 95!"

    def test_process_variables_nested(self, agent_factory):
        """Test variable processing with nested variables."""
        text = "User: {{user.name}}, Email: {{user.email}}"
        variables = {
            "user": {
                "name": "Bob",
                "email": "<EMAIL>"
            }
        }
        
        result = agent_factory.process_variables(text, variables)
        
        assert result == "User: Bob, Email: <EMAIL>"

    def test_process_variables_missing_variable(self, agent_factory):
        """Test variable processing with missing variable."""
        text = "Hello {{name}}, your score is {{missing}}!"
        variables = {"name": "Alice"}
        
        result = agent_factory.process_variables(text, variables)
        
        # Missing variables should remain as placeholders
        assert result == "Hello Alice, your score is {{missing}}!"

    def test_process_variables_no_variables(self, agent_factory):
        """Test variable processing with no variables."""
        text = "Hello world!"
        variables = {}
        
        result = agent_factory.process_variables(text, variables)
        
        assert result == "Hello world!"

    def test_process_variables_empty_text(self, agent_factory):
        """Test variable processing with empty text."""
        text = ""
        variables = {"name": "Alice"}
        
        result = agent_factory.process_variables(text, variables)
        
        assert result == ""