"""
Unit tests for agent configuration schemas.

This module contains comprehensive unit tests for the agent configuration
Pydantic models, covering validation, serialization, and error handling.
"""

import pytest
from pydantic import ValidationError

from app.schemas.agent_config import (
    AgentConfig,
    ToolConfig,
    AgentTool
)


class TestToolConfig:
    """Test class for ToolConfig schema."""

    def test_tool_config_valid(self):
        """Test valid ToolConfig creation."""
        config_data = {
            "name": "web_search",
            "description": "Search the web for information",
            "parameters": {
                "query": {"type": "string", "description": "Search query"}
            }
        }
        
        config = ToolConfig(**config_data)
        
        assert config.name == "web_search"
        assert config.description == "Search the web for information"
        assert config.parameters["query"]["type"] == "string"

    def test_tool_config_minimal(self):
        """Test ToolConfig with minimal required fields."""
        config_data = {
            "name": "simple_tool",
            "description": "A simple tool"
        }
        
        config = ToolConfig(**config_data)
        
        assert config.name == "simple_tool"
        assert config.description == "A simple tool"
        assert config.parameters == {}  # Default

    def test_tool_config_empty_name(self):
        """Test ToolConfig with empty name."""
        config_data = {
            "name": "",
            "description": "Test tool"
        }
        
        with pytest.raises(ValidationError) as exc_info:
            ToolConfig(**config_data)
        
        assert "name" in str(exc_info.value)


class TestAgentTool:
    """Test class for AgentTool schema."""

    def test_agent_tool_valid(self):
        """Test valid AgentTool creation."""
        tool_data = {
            "tool_type": "workflow",
            "url": "http://localhost:5000/execute-by-name",
            "workflow": {
                "workflow_id": "test_workflow",
                "approval": "False",
                "description": "Test workflow",
                "payload": {
                    "user_dependent_fields": ["topic"],
                    "user_payload_template": {"topic": ""}
                }
            }
        }
        
        tool = AgentTool(**tool_data)
        
        assert tool.tool_type == "workflow"
        assert tool.url == "http://localhost:5000/execute-by-name"
        assert tool.workflow["workflow_id"] == "test_workflow"

    def test_agent_tool_missing_required_fields(self):
        """Test AgentTool with missing required fields."""
        tool_data = {
            "tool_type": "workflow"
            # Missing url and workflow
        }
        
        with pytest.raises(ValidationError) as exc_info:
            AgentTool(**tool_data)
        
        error_str = str(exc_info.value)
        assert "url" in error_str or "workflow" in error_str


class TestAgentConfig:
    """Test class for AgentConfig schema."""

    def test_agent_config_valid_complete(self):
        """Test valid complete AgentConfig creation."""
        config_data = {
            "name": "Test Agent",
            "description": "A comprehensive test agent",
            "agent_type": "autogen",
            "ai_model_config": {
                "model": "gpt-4",
                "api_key": "test-api-key"
            },
            "system_message": "You are a helpful test assistant.",
            "tools": [
                {
                    "tool_type": "workflow",
                    "url": "http://localhost:5000/execute-by-name",
                    "workflow": {
                        "workflow_id": "test_workflow",
                        "approval": "False",
                        "description": "Test workflow",
                        "payload": {
                            "user_dependent_fields": ["topic"],
                            "user_payload_template": {"topic": ""}
                        }
                    }
                }
            ],
            "memory_enabled": True,
            "memory_config": {"initial_entries": []}
        }
        
        config = AgentConfig(**config_data)
        
        assert config.name == "Test Agent"
        assert config.description == "A comprehensive test agent"
        assert config.agent_type == "autogen"
        assert config.system_message == "You are a helpful test assistant."
        assert config.ai_model_config["model"] == "gpt-4"
        assert len(config.tools) == 1
        assert config.tools[0].tool_type == "workflow"
        assert config.memory_enabled is True
        assert config.memory_config["initial_entries"] == []

    def test_agent_config_minimal(self):
        """Test AgentConfig with minimal required fields."""
        config_data = {
            "name": "Minimal Agent",
            "description": "A minimal agent",
            "agent_type": "autogen",
            "ai_model_config": {
                "model": "gpt-4o-mini",
                "api_key": "test-key"
            },
            "system_message": "You are a helpful AI assistant."
        }
        
        config = AgentConfig(**config_data)
        
        assert config.name == "Minimal Agent"
        assert config.description == "A minimal agent"
        assert config.agent_type == "autogen"
        assert config.system_message == "You are a helpful AI assistant."
        assert config.ai_model_config["model"] == "gpt-4o-mini"
        # Check defaults
        assert config.tools == []
        assert config.memory_enabled is False
        assert config.memory_config is None

    def test_agent_config_missing_required_fields(self):
        """Test AgentConfig with missing required fields."""
        config_data = {
            "name": "Test Agent"
            # Missing required fields
        }
        
        with pytest.raises(ValidationError) as exc_info:
            AgentConfig(**config_data)
        
        error_str = str(exc_info.value)
        required_fields = [
            "description", "agent_type", "ai_model_config", "system_message"
        ]
        assert any(field in error_str for field in required_fields)

    def test_agent_config_empty_name(self):
        """Test AgentConfig with empty name."""
        config_data = {
            "name": "",  # Invalid: empty name
            "description": "Test agent",
            "agent_type": "autogen",
            "ai_model_config": {"model": "gpt-4", "api_key": "test"},
            "system_message": "Test message"
        }
        
        with pytest.raises(ValidationError) as exc_info:
            AgentConfig(**config_data)
        
        assert "name" in str(exc_info.value)

    def test_agent_config_serialization(self):
        """Test AgentConfig serialization to dict."""
        config_data = {
            "name": "Serialization Test Agent",
            "description": "Test serialization",
            "agent_type": "autogen",
            "ai_model_config": {
                "model": "gpt-4",
                "api_key": "test-key"
            },
            "system_message": "You are a test assistant.",
            "tools": [
                {
                    "tool_type": "workflow",
                    "url": "http://localhost:5000/test",
                    "workflow": {
                        "workflow_id": "test",
                        "approval": "False",
                        "description": "Test",
                        "payload": {}
                    }
                }
            ],
            "memory_enabled": False
        }
        
        config = AgentConfig(**config_data)
        serialized = config.model_dump()
        
        assert isinstance(serialized, dict)
        assert serialized["name"] == "Serialization Test Agent"
        assert serialized["agent_type"] == "autogen"
        assert len(serialized["tools"]) == 1
        assert serialized["tools"][0]["tool_type"] == "workflow"
        assert serialized["memory_enabled"] is False

    def test_agent_config_deserialization(self):
        """Test AgentConfig deserialization from dict."""
        config_dict = {
            "name": "Deserialization Test Agent",
            "description": "Test deserialization",
            "agent_type": "autogen",
            "ai_model_config": {
                "model": "gpt-3.5-turbo",
                "api_key": "test-key"
            },
            "system_message": "Custom system message",
            "memory_enabled": True,
            "memory_config": {"test": "config"}
        }
        
        config = AgentConfig.model_validate(config_dict)
        
        assert config.name == "Deserialization Test Agent"
        assert config.description == "Test deserialization"
        assert config.agent_type == "autogen"
        assert config.system_message == "Custom system message"
        assert config.ai_model_config["model"] == "gpt-3.5-turbo"
        assert config.memory_enabled is True
        assert config.memory_config["test"] == "config"

    def test_agent_config_with_multiple_tools(self):
        """Test AgentConfig with multiple tools validation."""
        config_data = {
            "name": "Multi-Tool Agent",
            "description": "Agent with multiple tools",
            "agent_type": "autogen",
            "ai_model_config": {
                "model": "gpt-4",
                "api_key": "test-key"
            },
            "system_message": "You are a multi-tool assistant.",
            "tools": [
                {
                    "tool_type": "workflow",
                    "url": "http://localhost:5000/tool1",
                    "workflow": {
                        "workflow_id": "tool1",
                        "approval": "False",
                        "description": "First tool",
                        "payload": {}
                    }
                },
                {
                    "tool_type": "workflow",
                    "url": "http://localhost:5000/tool2",
                    "workflow": {
                        "workflow_id": "tool2",
                        "approval": "True",
                        "description": "Second tool",
                        "payload": {}
                    }
                }
            ]
        }
        
        config = AgentConfig(**config_data)
        
        assert len(config.tools) == 2
        assert config.tools[0].workflow["workflow_id"] == "tool1"
        assert config.tools[1].workflow["workflow_id"] == "tool2"
        assert config.tools[0].workflow["approval"] == "False"
        assert config.tools[1].workflow["approval"] == "True"

    def test_agent_config_json_serialization(self):
        """Test AgentConfig JSON serialization and deserialization."""
        config_data = {
            "name": "JSON Test Agent",
            "description": "Test JSON operations",
            "agent_type": "autogen",
            "ai_model_config": {
                "model": "gpt-4",
                "api_key": "test-key"
            },
            "system_message": "You are a JSON test assistant.",
            "memory_enabled": True,
            "memory_config": {"format": "json", "test": True}
        }
        
        config = AgentConfig(**config_data)
        
        # Test JSON serialization
        json_str = config.model_dump_json()
        assert isinstance(json_str, str)
        
        # Test JSON deserialization
        config_from_json = AgentConfig.model_validate_json(json_str)
        assert config_from_json.name == config.name
        assert config_from_json.description == config.description
        assert config_from_json.memory_config == config.memory_config