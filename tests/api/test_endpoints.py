import pytest

# Optional FastAPI imports
try:
    from fastapi import status
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False
    status = None

try:
    from app.api.models import ChatRequest, AgentResponse
except ImportError:
    ChatRequest = None
    AgentResponse = None


def test_health_check(test_client):
    if not FASTAPI_AVAILABLE:
        pytest.skip("FastAPI not available")
    response = test_client.get("/health")
    assert response.status_code == status.HTTP_200_OK
    assert response.json() == {"status": "healthy"}


def test_chat_endpoint(test_client):
    if not FASTAPI_AVAILABLE:
        pytest.skip("FastAPI not available")
    request_data = {
        "message": "Hello",
        "session_id": "test-session",
        "context": {}
    }
    response = test_client.post("/chat", json=request_data)
    assert response.status_code == status.HTTP_200_OK
    assert "response" in response.json()


def test_invalid_chat_request(test_client):
    if not FASTAPI_AVAILABLE:
        pytest.skip("FastAPI not available")
    request_data = {
        "message": "",  # Invalid empty message
        "session_id": "test-session",
    }
    response = test_client.post("/chat", json=request_data)
    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


@pytest.mark.asyncio
async def test_stream_chat(test_client):
    if not FASTAPI_AVAILABLE:
        pytest.skip("FastAPI not available")
    request_data = {
        "message": "Hello",
        "session_id": "test-session",
        "stream": True
    }
    response = test_client.post("/chat/stream", json=request_data)
    assert response.status_code == status.HTTP_200_OK
    assert "text/event-stream" in response.headers["content-type"]
