"""
Unit tests for RedisClient class.

This module contains comprehensive unit tests for the RedisClient class,
covering all Redis operations, serialization, and error handling.
"""

import json
from unittest.mock import AsyncMock, patch

import pytest
from redis.asyncio import Redis
from redis.exceptions import ConnectionError, RedisError

from app.helper.redis_client import RedisClient


class TestRedisClient:
    """Test class for RedisClient."""

    @pytest.fixture
    def redis_client(self):
        """Create RedisClient instance for testing."""
        return RedisClient()

    @pytest.fixture
    def mock_redis(self):
        """Create mock Redis instance for testing."""
        mock_redis = AsyncMock(spec=Redis)
        return mock_redis

    def test_init(self, redis_client):
        """Test RedisClient initialization."""
        assert redis_client.redis is None
        assert redis_client.logger is not None

    @pytest.mark.asyncio
    async def test_initialize_success(self, redis_client):
        """Test successful Redis initialization."""
        with patch("app.helper.redis_client.Redis") as mock_redis_class:
            mock_redis = AsyncMock()
            mock_redis_class.return_value = mock_redis
            mock_redis.ping.return_value = True

            await redis_client.initialize()

            assert redis_client.redis == mock_redis
            mock_redis.ping.assert_called_once()

    @pytest.mark.asyncio
    async def test_initialize_connection_failure(self, redis_client):
        """Test Redis initialization with connection failure."""
        with patch("app.helper.redis_client.Redis") as mock_redis_class:
            mock_redis = AsyncMock()
            mock_redis_class.return_value = mock_redis
            mock_redis.ping.side_effect = ConnectionError("Connection failed")

            await redis_client.initialize()

            # Should handle gracefully
            assert redis_client.redis == mock_redis

    def test_serialize_value_dict(self, redis_client):
        """Test serializing dictionary value."""
        test_dict = {"key": "value", "number": 42}
        result = redis_client.serialize_value(test_dict)

        assert result == json.dumps(test_dict)

    def test_serialize_value_string(self, redis_client):
        """Test serializing string value."""
        test_string = "simple string"
        result = redis_client.serialize_value(test_string)

        assert result == test_string

    def test_serialize_value_number(self, redis_client):
        """Test serializing number value."""
        test_number = 42
        result = redis_client.serialize_value(test_number)

        assert result == str(test_number)

    def test_deserialize_value_json(self, redis_client):
        """Test deserializing JSON value."""
        test_dict = {"key": "value", "number": 42}
        json_string = json.dumps(test_dict)

        result = redis_client.deserialize_value(json_string)

        assert result == test_dict

    def test_deserialize_value_string(self, redis_client):
        """Test deserializing plain string value."""
        test_string = "simple string"

        result = redis_client.deserialize_value(test_string)

        assert result == test_string

    def test_deserialize_value_with_default_type(self, redis_client):
        """Test deserializing with default type."""
        invalid_json = "not json"
        default_value = []

        result = redis_client.deserialize_value(invalid_json, default_value)

        assert result == default_value

    @pytest.mark.asyncio
    async def test_set_hash_success(self, redis_client, mock_redis):
        """Test successful hash setting."""
        redis_client.redis = mock_redis

        test_key = "test_hash"
        test_data = {"field1": "value1", "field2": "value2"}
        test_ttl = 3600

        await redis_client.set_hash(test_key, test_data, test_ttl)

        mock_redis.hset.assert_called_once()
        mock_redis.expire.assert_called_once_with(test_key, test_ttl)

    @pytest.mark.asyncio
    async def test_set_hash_no_ttl(self, redis_client, mock_redis):
        """Test hash setting without TTL."""
        redis_client.redis = mock_redis

        test_key = "test_hash"
        test_data = {"field1": "value1"}

        await redis_client.set_hash(test_key, test_data)

        mock_redis.hset.assert_called_once()
        mock_redis.expire.assert_not_called()

    @pytest.mark.asyncio
    async def test_set_hash_redis_error(self, redis_client, mock_redis):
        """Test hash setting with Redis error."""
        redis_client.redis = mock_redis
        mock_redis.hset.side_effect = RedisError("Redis error")

        test_key = "test_hash"
        test_data = {"field1": "value1"}

        # Should handle gracefully
        await redis_client.set_hash(test_key, test_data)

    @pytest.mark.asyncio
    async def test_get_hash_success(self, redis_client, mock_redis):
        """Test successful hash retrieval."""
        redis_client.redis = mock_redis

        test_key = "test_hash"
        mock_hash_data = {
            b"field1": b'{"key": "value"}',
            b"field2": b"simple_value"
        }
        mock_redis.hgetall.return_value = mock_hash_data

        result = await redis_client.get_hash(test_key)

        expected = {"field1": {"key": "value"}, "field2": "simple_value"}
        assert result == expected

    @pytest.mark.asyncio
    async def test_get_hash_empty(self, redis_client, mock_redis):
        """Test hash retrieval when hash is empty."""
        redis_client.redis = mock_redis
        mock_redis.hgetall.return_value = {}

        result = await redis_client.get_hash("nonexistent_hash")

        assert result == {}

    @pytest.mark.asyncio
    async def test_exists_true(self, redis_client, mock_redis):
        """Test key existence check when key exists."""
        redis_client.redis = mock_redis
        mock_redis.exists.return_value = 1

        result = await redis_client.exists("existing_key")

        assert result is True

    @pytest.mark.asyncio
    async def test_exists_false(self, redis_client, mock_redis):
        """Test key existence check when key doesn't exist."""
        redis_client.redis = mock_redis
        mock_redis.exists.return_value = 0

        result = await redis_client.exists("nonexistent_key")

        assert result is False

    @pytest.mark.asyncio
    async def test_delete_success(self, redis_client, mock_redis):
        """Test successful key deletion."""
        redis_client.redis = mock_redis

        await redis_client.delete("test_key")

        mock_redis.delete.assert_called_once_with("test_key")

    @pytest.mark.asyncio
    async def test_set_value_with_ttl(self, redis_client, mock_redis):
        """Test setting value with TTL."""
        redis_client.redis = mock_redis

        test_key = "test_key"
        test_value = {"data": "test"}
        test_ttl = 3600

        await redis_client.set_value(test_key, test_value, test_ttl)

        mock_redis.set.assert_called_once_with(
            test_key, json.dumps(test_value), ex=test_ttl
        )

    @pytest.mark.asyncio
    async def test_set_value_no_ttl(self, redis_client, mock_redis):
        """Test setting value without TTL."""
        redis_client.redis = mock_redis

        test_key = "test_key"
        test_value = "simple_value"

        await redis_client.set_value(test_key, test_value)

        mock_redis.set.assert_called_once_with(test_key, test_value)

    @pytest.mark.asyncio
    async def test_get_value_success(self, redis_client, mock_redis):
        """Test successful value retrieval."""
        redis_client.redis = mock_redis
        test_value = {"data": "test"}
        mock_redis.get.return_value = json.dumps(test_value).encode()

        result = await redis_client.get_value("test_key")

        assert result == test_value

    @pytest.mark.asyncio
    async def test_get_value_none(self, redis_client, mock_redis):
        """Test value retrieval when key doesn't exist."""
        redis_client.redis = mock_redis
        mock_redis.get.return_value = None

        result = await redis_client.get_value("nonexistent_key")

        assert result is None

    @pytest.mark.asyncio
    async def test_increment_success(self, redis_client, mock_redis):
        """Test successful increment operation."""
        redis_client.redis = mock_redis
        mock_redis.incr.return_value = 5

        result = await redis_client.increment("counter_key", 2)

        assert result == 5
        mock_redis.incr.assert_called_once_with("counter_key", 2)

    @pytest.mark.asyncio
    async def test_rpush_success(self, redis_client, mock_redis):
        """Test successful right push to list."""
        redis_client.redis = mock_redis

        test_values = ["value1", "value2", "value3"]
        await redis_client.rpush("list_key", *test_values)

        mock_redis.rpush.assert_called_once_with("list_key", *test_values)

    @pytest.mark.asyncio
    async def test_set_list_with_ttl(self, redis_client, mock_redis):
        """Test setting list with TTL."""
        redis_client.redis = mock_redis

        test_key = "list_key"
        test_values = ["item1", "item2", "item3"]
        test_ttl = 3600

        await redis_client.set_list(test_key, test_values, test_ttl)

        mock_redis.delete.assert_called_once_with(test_key)
        mock_redis.rpush.assert_called_once_with(test_key, *test_values)
        mock_redis.expire.assert_called_once_with(test_key, test_ttl)

    @pytest.mark.asyncio
    async def test_get_list_success(self, redis_client, mock_redis):
        """Test successful list retrieval."""
        redis_client.redis = mock_redis
        mock_redis.lrange.return_value = [b"item1", b"item2", b"item3"]

        result = await redis_client.get_list("list_key")

        expected = ["item1", "item2", "item3"]
        assert result == expected

    @pytest.mark.asyncio
    async def test_lrange_success(self, redis_client, mock_redis):
        """Test successful list range retrieval."""
        redis_client.redis = mock_redis
        mock_redis.lrange.return_value = [b"item2", b"item3"]

        result = await redis_client.lrange("list_key", 1, 2)

        expected = ["item2", "item3"]
        assert result == expected
        mock_redis.lrange.assert_called_once_with("list_key", 1, 2)

    @pytest.mark.asyncio
    async def test_publish_success(self, redis_client, mock_redis):
        """Test successful message publishing."""
        redis_client.redis = mock_redis

        await redis_client.publish("test_channel", "test_message")

        mock_redis.publish.assert_called_once_with(
            "test_channel", "test_message"
        )

    @pytest.mark.asyncio
    async def test_subscribe_success(self, redis_client, mock_redis):
        """Test successful channel subscription."""
        redis_client.redis = mock_redis
        mock_pubsub = AsyncMock()
        mock_redis.pubsub.return_value = mock_pubsub

        result = await redis_client.subscribe("test_channel")

        assert result == mock_pubsub
        mock_pubsub.subscribe.assert_called_once_with("test_channel")

    @pytest.mark.asyncio
    async def test_expire_success(self, redis_client, mock_redis):
        """Test successful key expiration setting."""
        redis_client.redis = mock_redis

        await redis_client.expire("test_key", 3600)

        mock_redis.expire.assert_called_once_with("test_key", 3600)

    @pytest.mark.asyncio
    async def test_flushdb_success(self, redis_client, mock_redis):
        """Test successful database flush."""
        redis_client.redis = mock_redis

        await redis_client.flushdb()

        mock_redis.flushdb.assert_called_once_with(asynchronous=True)

    @pytest.mark.asyncio
    async def test_wait_success(self, redis_client, mock_redis):
        """Test successful wait for replication."""
        redis_client.redis = mock_redis

        await redis_client.wait(2, 1000)

        mock_redis.wait.assert_called_once_with(2, 1000)

    @pytest.mark.asyncio
    async def test_close_success(self, redis_client, mock_redis):
        """Test successful connection close."""
        redis_client.redis = mock_redis

        await redis_client.close()

        mock_redis.close.assert_called_once()

    @pytest.mark.asyncio
    async def test_hset_success(self, redis_client, mock_redis):
        """Test successful hash field setting."""
        redis_client.redis = mock_redis

        test_data = {"nested": "value"}
        await redis_client.hset("hash_key", "field", test_data)

        mock_redis.hset.assert_called_once_with(
            "hash_key", "field", json.dumps(test_data)
        )

    @pytest.mark.asyncio
    async def test_hincr_success(self, redis_client, mock_redis):
        """Test successful hash field increment."""
        redis_client.redis = mock_redis
        mock_redis.hincrby.return_value = 10

        result = await redis_client.hincr("hash_key", "counter_field", 5)

        assert result == 10
        mock_redis.hincrby.assert_called_once_with(
            "hash_key", "counter_field", 5
        )

    @pytest.mark.asyncio
    async def test_scan_success(self, redis_client, mock_redis):
        """Test successful key scanning."""
        redis_client.redis = mock_redis
        mock_redis.scan.return_value = (0, [b"key1", b"key2", b"key3"])

        result = await redis_client.scan("test_*", 50)

        expected = ["key1", "key2", "key3"]
        assert result == expected
        mock_redis.scan.assert_called_once_with(
            cursor=0, match="test_*", count=50
        )

    @pytest.mark.asyncio
    async def test_set_binary_success(self, redis_client, mock_redis):
        """Test successful binary value setting."""
        redis_client.redis = mock_redis

        test_data = b"binary data"
        await redis_client.set_binary("binary_key", test_data, 3600)

        mock_redis.set.assert_called_once_with(
            "binary_key", test_data, ex=3600
        )

    @pytest.mark.asyncio
    async def test_get_binary_success(self, redis_client, mock_redis):
        """Test successful binary value retrieval."""
        redis_client.redis = mock_redis
        test_data = b"binary data"
        mock_redis.get.return_value = test_data

        result = await redis_client.get_binary("binary_key")

        assert result == test_data

    @pytest.mark.asyncio
    async def test_ensure_connection_no_redis(self, redis_client):
        """Test connection ensuring when Redis is not initialized."""
        redis_client.redis = None

        with patch.object(redis_client, "initialize") as mock_init:
            await redis_client._ensure_connection()
            mock_init.assert_called_once()

    @pytest.mark.asyncio
    async def test_ensure_connection_with_redis(
        self, redis_client, mock_redis
    ):
        """Test connection ensuring when Redis is already initialized."""
        redis_client.redis = mock_redis

        with patch.object(redis_client, "initialize") as mock_init:
            await redis_client._ensure_connection()
            mock_init.assert_not_called()
