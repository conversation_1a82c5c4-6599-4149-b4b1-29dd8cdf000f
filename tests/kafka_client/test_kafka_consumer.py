"""
Unit tests for KafkaConsumer class.

This module contains comprehensive unit tests for the KafkaConsumer class,
covering message processing, agent operations, and error handling.
"""

import asyncio
import json
import pytest
from unittest.mock import AsyncMock, Mock, patch
from aiokafka import AIOKafkaConsumer, ConsumerRecord

from app.kafka_client.consumer import KafkaConsumer


class TestKafkaConsumer:
    """Test class for KafkaConsumer."""

    @pytest.fixture
    def kafka_consumer(self):
        """Create KafkaConsumer instance for testing."""
        return KafkaConsumer()

    @pytest.fixture
    def mock_consumer_record(self):
        """Create mock ConsumerRecord for testing."""
        record = Mock(spec=ConsumerRecord)
        record.topic = "test_topic"
        record.partition = 0
        record.offset = 123
        record.key = b"test_key"
        record.value = json.dumps({"test": "data"}).encode('utf-8')
        record.headers = [(b'content-type', b'application/json')]
        return record

    @pytest.fixture
    def mock_aiokafka_consumer(self):
        """Create mock AIOKafkaConsumer for testing."""
        mock_consumer = AsyncMock(spec=AIOKafkaConsumer)
        mock_consumer.start = AsyncMock()
        mock_consumer.stop = AsyncMock()
        mock_consumer.__aiter__ = AsyncMock()
        return mock_consumer

    def test_init(self, kafka_consumer):
        """Test KafkaConsumer initialization."""
        assert kafka_consumer.consumer is None
        assert kafka_consumer.logger is not None
        assert kafka_consumer.semaphore._value == 10  # Default concurrency

    @pytest.mark.asyncio
    async def test_start_consumer_success(self, kafka_consumer):
        """Test successful consumer startup."""
        with patch(
            'app.kafka_client.consumer.AIOKafkaConsumer'
        ) as mock_consumer_class:
            mock_consumer = AsyncMock()
            mock_consumer_class.return_value = mock_consumer
            
            # Mock the async iteration
            mock_consumer.__aiter__.return_value = iter([])
            
            # Start consumer in background task
            task = asyncio.create_task(kafka_consumer.start_consumer())
            await asyncio.sleep(0.1)  # Let it start
            task.cancel()
            
            try:
                await task
            except asyncio.CancelledError:
                pass
            
            mock_consumer.start.assert_called_once()

    @pytest.mark.asyncio
    async def test_start_consumer_failure(self, kafka_consumer):
        """Test consumer startup failure."""
        with patch(
            'app.kafka_client.consumer.AIOKafkaConsumer'
        ) as mock_consumer_class:
            mock_consumer = AsyncMock()
            mock_consumer.start.side_effect = Exception("Connection failed")
            mock_consumer_class.return_value = mock_consumer
            
            task = asyncio.create_task(kafka_consumer.start_consumer())
            await asyncio.sleep(0.1)
            task.cancel()
            
            try:
                await task
            except asyncio.CancelledError:
                pass

    @pytest.mark.asyncio
    async def test_process_message_task_success(
        self, kafka_consumer, mock_consumer_record
    ):
        """Test successful message processing."""
        with patch.object(
            kafka_consumer, 'process_agent_creation'
        ) as mock_process:
            mock_process.return_value = None
            mock_consumer_record.topic = "agent_creation"
            
            await kafka_consumer.process_message_task(mock_consumer_record)
            
            mock_process.assert_called_once()

    @pytest.mark.asyncio
    async def test_process_message_task_invalid_json(
        self, kafka_consumer, mock_consumer_record
    ):
        """Test message processing with invalid JSON."""
        mock_consumer_record.value = b"invalid json"
        mock_consumer_record.topic = "agent_creation"
        
        # Should not raise exception
        await kafka_consumer.process_message_task(mock_consumer_record)

    @pytest.mark.asyncio
    async def test_process_agent_creation_success(self, kafka_consumer):
        """Test successful agent creation processing."""
        test_message = {
            "user_id": "test_user",
            "agent_config": {
                "name": "test_agent",
                "description": "Test agent",
                "system_prompt": "You are a test agent"
            },
            "session_id": "test_session"
        }
        
        with patch('app.kafka_client.consumer.AgentFactory') as mock_factory:
            mock_agent_factory = AsyncMock()
            mock_factory.return_value = mock_agent_factory
            mock_agent_factory.create_agent.return_value = Mock()
            
            await kafka_consumer.process_agent_creation(test_message)
            
            mock_agent_factory.create_agent.assert_called_once()

    @pytest.mark.asyncio
    async def test_process_agent_creation_missing_fields(self, kafka_consumer):
        """Test agent creation with missing required fields."""
        test_message = {
            "user_id": "test_user"
            # Missing agent_config and session_id
        }
        
        # Should handle gracefully without raising exception
        await kafka_consumer.process_agent_creation(test_message)

    @pytest.mark.asyncio
    async def test_process_agent_chat_success(self, kafka_consumer):
        """Test successful agent chat processing."""
        test_message = {
            "session_id": "test_session",
            "user_message": "Hello, agent!",
            "user_id": "test_user"
        }
        
        with patch(
            'app.kafka_client.consumer.ChatProcessor'
        ) as mock_processor:
            mock_chat_processor = AsyncMock()
            mock_processor.return_value = mock_chat_processor
            mock_chat_processor.process_chat.return_value = "Agent response"
            
            await kafka_consumer.process_agent_chat(test_message)
            
            mock_chat_processor.process_chat.assert_called_once()

    @pytest.mark.asyncio
    async def test_process_agent_query_success(self, kafka_consumer):
        """Test successful agent query processing."""
        test_message = {
            "session_id": "test_session",
            "query": "What is the weather?",
            "user_id": "test_user",
            "agent_configs": [
                {
                    "name": "weather_agent",
                    "description": "Weather information agent"
                }
            ]
        }
        
        with patch('app.kafka_client.consumer.AgentFactory') as mock_factory:
            mock_agent_factory = AsyncMock()
            mock_factory.return_value = mock_agent_factory
            mock_agent_factory.process_with_agents.return_value = (
                "Weather info"
            )
            
            await kafka_consumer.process_agent_query(test_message)
            
            mock_agent_factory.process_with_agents.assert_called_once()

    @pytest.mark.asyncio
    async def test_process_agent_message_success(self, kafka_consumer):
        """Test successful agent message processing."""
        test_message = {
            "session_id": "test_session",
            "message": "Process this message",
            "user_id": "test_user",
            "agent_config": {
                "name": "message_agent",
                "description": "Message processing agent"
            }
        }
        
        with patch('app.kafka_client.consumer.AgentFactory') as mock_factory:
            mock_agent_factory = AsyncMock()
            mock_factory.return_value = mock_agent_factory
            mock_agent_factory.process_with_agents.return_value = "Processed"
            
            await kafka_consumer.process_agent_message(test_message)
            
            mock_agent_factory.process_with_agents.assert_called_once()

    @pytest.mark.asyncio
    async def test_process_agent_session_deletion_success(
        self, kafka_consumer
    ):
        """Test successful session deletion processing."""
        test_message = {
            "session_id": "test_session",
            "user_id": "test_user"
        }
        
        with patch('app.kafka_client.consumer.SessionManager') as mock_manager:
            mock_session_manager = AsyncMock()
            mock_manager.return_value = mock_session_manager
            mock_session_manager.delete_session.return_value = True
            
            await kafka_consumer.process_agent_session_deletion(test_message)
            
            mock_session_manager.delete_session.assert_called_once_with(
                "test_session"
            )

    @pytest.mark.asyncio
    async def test_process_orchestration_team_session_success(
        self, kafka_consumer
    ):
        """Test successful orchestration team session processing."""
        test_message = {
            "group_id": "test_group",
            "user_id": "test_user",
            "session_id": "test_session"
        }
        
        with patch(
            'app.kafka_client.consumer.OrchestrationSessionManager'
        ) as mock_manager:
            mock_orchestration_manager = AsyncMock()
            mock_manager.return_value = mock_orchestration_manager
            mock_orchestration_manager.create_session.return_value = (
                "session_created"
            )
            
            await kafka_consumer.process_orchestration_team_session(
                test_message
            )
            
            mock_orchestration_manager.create_session.assert_called_once()

    @pytest.mark.asyncio
    async def test_process_orchestration_team_chat_success(
        self, kafka_consumer
    ):
        """Test successful orchestration team chat processing."""
        test_message = {
            "session_id": "test_session",
            "user_message": "Hello team!",
            "user_id": "test_user"
        }
        
        with patch(
            'app.kafka_client.consumer.OrchestrationSessionManager'
        ) as mock_manager:
            mock_orchestration_manager = AsyncMock()
            mock_manager.return_value = mock_orchestration_manager
            mock_orchestration_manager.process_chat.return_value = (
                "Team response"
            )
            
            await kafka_consumer.process_orchestration_team_chat(test_message)
            
            mock_orchestration_manager.process_chat.assert_called_once()

    @pytest.mark.asyncio
    async def test_process_human_input_response_success(self, kafka_consumer):
        """Test successful human input response processing."""
        test_message = {
            "session_id": "test_session",
            "human_input": "User response",
            "user_id": "test_user"
        }
        
        with patch(
            'app.kafka_client.consumer.OrchestrationSessionManager'
        ) as mock_manager:
            mock_orchestration_manager = AsyncMock()
            mock_manager.return_value = mock_orchestration_manager
            mock_orchestration_manager.handle_human_input.return_value = None
            
            await kafka_consumer.process_human_input_response(test_message)
            
            mock_orchestration_manager.handle_human_input.assert_called_once()

    @pytest.mark.asyncio
    async def test_process_agent_chat_stop_success(self, kafka_consumer):
        """Test successful agent chat stop processing."""
        test_message = {
            "session_id": "test_session",
            "user_id": "test_user"
        }
        
        with patch.object(
            kafka_consumer, '_stop_single_session', return_value=True
        ) as mock_stop:
            await kafka_consumer.process_agent_chat_stop(test_message)
            
            mock_stop.assert_called_once_with("test_session")

    @pytest.mark.asyncio
    async def test_stop_single_session_success(self, kafka_consumer):
        """Test successful single session stop."""
        session_id = "test_session"
        
        with patch('app.kafka_client.consumer.SessionManager') as mock_manager:
            mock_session_manager = AsyncMock()
            mock_manager.return_value = mock_session_manager
            mock_session_manager.end_session.return_value = None
            
            result = await kafka_consumer._stop_single_session(session_id)
            
            assert result is True
            mock_session_manager.end_session.assert_called_once_with(
                session_id
            )

    @pytest.mark.asyncio
    async def test_stop_single_session_failure(self, kafka_consumer):
        """Test single session stop failure."""
        session_id = "test_session"
        
        with patch('app.kafka_client.consumer.SessionManager') as mock_manager:
            mock_session_manager = AsyncMock()
            mock_manager.return_value = mock_session_manager
            mock_session_manager.end_session.side_effect = Exception(
                "Stop failed"
            )
            
            result = await kafka_consumer._stop_single_session(session_id)
            
            assert result is False

    @pytest.mark.asyncio
    async def test_send_token_usage_if_present_with_usage(
        self, kafka_consumer
    ):
        """Test token usage sending when usage data is present."""
        response_data = {
            "usage": {
                "prompt_tokens": 100,
                "completion_tokens": 50,
                "total_tokens": 150
            }
        }
        session_id = "test_session"
        agent_name = "test_agent"
        
        with patch('app.kafka_client.consumer.KafkaProducer') as mock_producer:
            mock_kafka_producer = AsyncMock()
            mock_producer.return_value = mock_kafka_producer
            mock_kafka_producer.send_token_usage.return_value = True
            
            await kafka_consumer._send_token_usage_if_present(
                response_data, session_id, agent_name
            )
            
            mock_kafka_producer.send_token_usage.assert_called_once()

    @pytest.mark.asyncio
    async def test_send_token_usage_if_present_no_usage(self, kafka_consumer):
        """Test token usage sending when no usage data is present."""
        response_data = {"message": "No usage data"}
        session_id = "test_session"
        agent_name = "test_agent"
        
        with patch('app.kafka_client.consumer.KafkaProducer') as mock_producer:
            mock_kafka_producer = AsyncMock()
            mock_producer.return_value = mock_kafka_producer
            
            await kafka_consumer._send_token_usage_if_present(
                response_data, session_id, agent_name
            )
            
            # Should not call send_token_usage
            mock_kafka_producer.send_token_usage.assert_not_called()

    @pytest.mark.asyncio
    async def test_message_routing_by_topic(self, kafka_consumer):
        """Test that messages are routed to correct handlers by topic."""
        topics_and_handlers = [
            ("agent_creation", "process_agent_creation"),
            ("agent_chat", "process_agent_chat"),
            ("agent_query", "process_agent_query"),
            ("agent_message", "process_agent_message"),
            ("agent_session_deletion", "process_agent_session_deletion"),
            (
                "orchestration_team_session",
                "process_orchestration_team_session"
            ),
            ("orchestration_team_chat", "process_orchestration_team_chat"),
            ("human_input_response", "process_human_input_response"),
            ("agent_chat_stop", "process_agent_chat_stop"),
        ]
        
        for topic, handler_name in topics_and_handlers:
            mock_record = Mock(spec=ConsumerRecord)
            mock_record.topic = topic
            mock_record.value = json.dumps({"test": "data"}).encode('utf-8')
            mock_record.headers = []
            
            with patch.object(
                kafka_consumer, handler_name
            ) as mock_handler:
                mock_handler.return_value = None
                
                await kafka_consumer.process_message_task(mock_record)
                
                mock_handler.assert_called_once()

    @pytest.mark.asyncio
    async def test_unknown_topic_handling(self, kafka_consumer):
        """Test handling of unknown topic."""
        mock_record = Mock(spec=ConsumerRecord)
        mock_record.topic = "unknown_topic"
        mock_record.value = json.dumps({"test": "data"}).encode('utf-8')
        mock_record.headers = []
        
        # Should not raise exception
        await kafka_consumer.process_message_task(mock_record)

    @pytest.mark.asyncio
    async def test_exception_handling_in_message_processing(
        self, kafka_consumer, mock_consumer_record
    ):
        """Test exception handling during message processing."""
        mock_consumer_record.topic = "agent_creation"
        
        with patch.object(
            kafka_consumer, 'process_agent_creation'
        ) as mock_process:
            mock_process.side_effect = Exception("Processing failed")
            
            # Should not raise exception
            await kafka_consumer.process_message_task(mock_consumer_record)