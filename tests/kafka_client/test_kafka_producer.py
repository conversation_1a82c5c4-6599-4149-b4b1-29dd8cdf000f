"""
Unit tests for KafkaProducer class.

This module contains comprehensive unit tests for the KafkaProducer class,
covering message sending, error handling, and producer lifecycle management.
"""

import json
import pytest
from unittest.mock import AsyncMock, Mock, patch
from aiokafka import AIOKafkaProducer
from aiokafka.errors import KafkaError, KafkaTimeoutError

from app.kafka_client.producer import KafkaProducer


class TestKafkaProducer:
    """Test class for KafkaProducer."""

    @pytest.fixture
    def kafka_producer(self):
        """Create KafkaProducer instance for testing."""
        return KafkaProducer()

    @pytest.fixture
    def mock_aiokafka_producer(self):
        """Create mock AIOKafkaProducer for testing."""
        mock_producer = AsyncMock(spec=AIOKafkaProducer)
        mock_producer.start = AsyncMock()
        mock_producer.stop = AsyncMock()
        mock_producer.send_and_wait = AsyncMock()
        return mock_producer

    def test_init(self, kafka_producer):
        """Test KafkaProducer initialization."""
        assert kafka_producer.producer is None
        assert kafka_producer.logger is not None

    @pytest.mark.asyncio
    async def test_start_producer_success(self, kafka_producer):
        """Test successful producer startup."""
        with patch(
            'app.kafka_client.producer.AIOKafkaProducer'
        ) as mock_producer_class:
            mock_producer = AsyncMock()
            mock_producer_class.return_value = mock_producer
            
            await kafka_producer.start_producer()
            
            assert kafka_producer.producer == mock_producer
            mock_producer.start.assert_called_once()

    @pytest.mark.asyncio
    async def test_start_producer_already_started(self, kafka_producer):
        """Test starting producer when already started."""
        kafka_producer.producer = Mock()
        
        await kafka_producer.start_producer()
        
        # Should not create new producer
        assert kafka_producer.producer is not None

    @pytest.mark.asyncio
    async def test_start_producer_failure(self, kafka_producer):
        """Test producer startup failure."""
        with patch(
            'app.kafka_client.producer.AIOKafkaProducer'
        ) as mock_producer_class:
            mock_producer = AsyncMock()
            mock_producer.start.side_effect = Exception("Connection failed")
            mock_producer_class.return_value = mock_producer
            
            await kafka_producer.start_producer()
            
            # Producer should be None on failure
            assert kafka_producer.producer is None

    @pytest.mark.asyncio
    async def test_stop_producer_success(
        self, kafka_producer, mock_aiokafka_producer
    ):
        """Test successful producer shutdown."""
        kafka_producer.producer = mock_aiokafka_producer
        
        await kafka_producer.stop_producer()
        
        mock_aiokafka_producer.stop.assert_called_once()
        assert kafka_producer.producer is None

    @pytest.mark.asyncio
    async def test_stop_producer_no_producer(self, kafka_producer):
        """Test stopping producer when none exists."""
        kafka_producer.producer = None
        
        # Should not raise exception
        await kafka_producer.stop_producer()

    @pytest.mark.asyncio
    async def test_send_message_success(
        self, kafka_producer, mock_aiokafka_producer
    ):
        """Test successful message sending."""
        kafka_producer.producer = mock_aiokafka_producer
        
        test_topic = "test_topic"
        test_message = {"key": "value", "data": "test"}
        test_headers = {"header1": "value1"}
        
        result = await kafka_producer.send_message(
            topic=test_topic,
            message=test_message,
            headers=test_headers
        )
        
        assert result is True
        mock_aiokafka_producer.send_and_wait.assert_called_once()
        
        # Verify call arguments
        call_args = mock_aiokafka_producer.send_and_wait.call_args
        assert call_args[1]['topic'] == test_topic
        assert json.loads(call_args[1]['value']) == test_message

    @pytest.mark.asyncio
    async def test_send_message_no_producer(self, kafka_producer):
        """Test sending message when producer is not initialized."""
        kafka_producer.producer = None
        
        result = await kafka_producer.send_message(
            topic="test_topic",
            message={"test": "data"}
        )
        
        assert result is False

    @pytest.mark.asyncio
    async def test_send_message_kafka_error(
        self, kafka_producer, mock_aiokafka_producer
    ):
        """Test message sending with Kafka error."""
        kafka_producer.producer = mock_aiokafka_producer
        mock_aiokafka_producer.send_and_wait.side_effect = KafkaError(
            "Kafka error"
        )
        
        result = await kafka_producer.send_message(
            topic="test_topic",
            message={"test": "data"}
        )
        
        assert result is False

    @pytest.mark.asyncio
    async def test_send_message_timeout_error(
        self, kafka_producer, mock_aiokafka_producer
    ):
        """Test message sending with timeout error."""
        kafka_producer.producer = mock_aiokafka_producer
        mock_aiokafka_producer.send_and_wait.side_effect = KafkaTimeoutError(
            "Timeout"
        )
        
        result = await kafka_producer.send_message(
            topic="test_topic",
            message={"test": "data"}
        )
        
        assert result is False

    @pytest.mark.asyncio
    async def test_send_message_generic_exception(
        self, kafka_producer, mock_aiokafka_producer
    ):
        """Test message sending with generic exception."""
        kafka_producer.producer = mock_aiokafka_producer
        mock_aiokafka_producer.send_and_wait.side_effect = Exception(
            "Generic error"
        )
        
        result = await kafka_producer.send_message(
            topic="test_topic",
            message={"test": "data"}
        )
        
        assert result is False

    @pytest.mark.asyncio
    async def test_send_token_usage_success(
        self, kafka_producer, mock_aiokafka_producer
    ):
        """Test successful token usage message sending."""
        kafka_producer.producer = mock_aiokafka_producer
        
        test_data = {
            "session_id": "test_session",
            "agent_name": "test_agent",
            "prompt_tokens": 100,
            "completion_tokens": 50,
            "total_tokens": 150
        }
        
        with patch.object(
            kafka_producer, 'send_message', return_value=True
        ) as mock_send:
            result = await kafka_producer.send_token_usage(**test_data)
            
            assert result is True
            mock_send.assert_called_once()
            
            # Verify the message structure
            call_args = mock_send.call_args
            assert call_args[1]['topic'] == 'token_usage'
            message = call_args[1]['message']
            assert message['session_id'] == test_data['session_id']
            assert message['agent_name'] == test_data['agent_name']
            assert (
                message['usage']['prompt_tokens'] == test_data['prompt_tokens']
            )
            assert (
                message['usage']['completion_tokens'] ==
                test_data['completion_tokens']
            )
            assert (
                message['usage']['total_tokens'] == test_data['total_tokens']
            )
            assert 'timestamp' in message

    @pytest.mark.asyncio
    async def test_send_token_usage_failure(
        self, kafka_producer, mock_aiokafka_producer
    ):
        """Test token usage message sending failure."""
        kafka_producer.producer = mock_aiokafka_producer
        
        with patch.object(
            kafka_producer, 'send_message', return_value=False
        ):
            result = await kafka_producer.send_token_usage(
                session_id="test_session",
                agent_name="test_agent",
                prompt_tokens=100,
                completion_tokens=50,
                total_tokens=150
            )
            
            assert result is False

    def test_get_current_timestamp(self, kafka_producer):
        """Test timestamp generation."""
        timestamp = kafka_producer._get_current_timestamp()
        
        assert isinstance(timestamp, str)
        assert len(timestamp) > 0
        # Should be in ISO format
        assert 'T' in timestamp
        assert timestamp.endswith('Z')

    def test_decode_headers_success(self, kafka_producer):
        """Test successful header decoding."""
        headers_list = [
            (b'header1', b'value1'),
            (b'header2', b'value2'),
            (b'content-type', b'application/json')
        ]
        
        result = kafka_producer.decode_headers(headers_list)
        
        expected = {
            'header1': 'value1',
            'header2': 'value2',
            'content-type': 'application/json'
        }
        assert result == expected

    def test_decode_headers_empty_list(self, kafka_producer):
        """Test decoding empty headers list."""
        result = kafka_producer.decode_headers([])
        assert result == {}

    def test_decode_headers_with_decode_error(self, kafka_producer):
        """Test header decoding with decode errors."""
        # Create headers with invalid UTF-8
        headers_list = [
            (b'valid_header', b'valid_value'),
            (b'\xff\xfe', b'invalid_key'),  # Invalid UTF-8
            (b'another_header', b'\xff\xfe')  # Invalid UTF-8 value
        ]
        
        result = kafka_producer.decode_headers(headers_list)
        
        # Should only include valid headers
        assert 'valid_header' in result
        assert result['valid_header'] == 'valid_value'
        # Invalid headers should be skipped
        assert len(result) == 1

    @pytest.mark.asyncio
    async def test_send_message_with_key(
        self, kafka_producer, mock_aiokafka_producer
    ):
        """Test sending message with partition key."""
        kafka_producer.producer = mock_aiokafka_producer
        
        test_topic = "test_topic"
        test_message = {"data": "test"}
        test_key = "partition_key"
        
        await kafka_producer.send_message(
            topic=test_topic,
            message=test_message,
            key=test_key
        )
        
        call_args = mock_aiokafka_producer.send_and_wait.call_args
        assert call_args[1]['key'] == test_key.encode('utf-8')

    @pytest.mark.asyncio
    async def test_send_message_serialization_error(
        self, kafka_producer, mock_aiokafka_producer
    ):
        """Test message sending with serialization error."""
        kafka_producer.producer = mock_aiokafka_producer
        
        # Create a message that can't be JSON serialized
        class NonSerializable:
            pass
        
        test_message = {"data": NonSerializable()}
        
        result = await kafka_producer.send_message(
            topic="test_topic",
            message=test_message
        )
        
        assert result is False