"""
Unit tests for PineconeMemory class.

This module contains comprehensive unit tests for the PineconeMemory class,
covering memory operations, embedding generation, and error handling.
"""

from unittest.mock import Mock, patch

import pytest
from autogen_agentchat.messages import ChatMessage

# Try to import ChatCompletionContext, use mock if not available
try:
    from autogen_core.models import ChatCompletionContext
except ImportError:
    # Create a mock class if the import fails
    class ChatCompletionContext:
        def __init__(self):
            self.messages = []

from app.memory.pinecone_memory import MemoryContent, PineconeMemory


class TestPineconeMemory:
    """Test class for PineconeMemory."""

    @pytest.fixture
    def memory_config(self):
        """Create memory configuration for testing."""
        return {
            "index_name": "test-index",
            "namespace": "test-namespace",
            "dimension": 1536,
            "metric": "cosine",
        }

    @pytest.fixture
    def pinecone_memory(self, memory_config):
        """Create PineconeMemory instance for testing."""
        with patch("app.memory.pinecone_memory.Pinecone"):
            memory = PineconeMemory(agent_id="test_agent", **memory_config)
            return memory

    @pytest.fixture
    def mock_pinecone_client(self):
        """Create mock Pinecone client for testing."""
        mock_client = Mock()
        mock_index = Mock()
        mock_client.Index.return_value = mock_index
        return mock_client, mock_index

    @pytest.fixture
    def sample_memory_content(self):
        """Create sample MemoryContent for testing."""
        return MemoryContent(
            content="This is a test memory",
            metadata={"type": "conversation", "timestamp": "2024-01-01"},
        )

    def test_init(self, pinecone_memory, memory_config):
        """Test PineconeMemory initialization."""
        assert pinecone_memory.agent_id == "test_agent"
        assert pinecone_memory.index_name == memory_config["index_name"]
        assert pinecone_memory.namespace == memory_config["namespace"]
        assert pinecone_memory.dimension == memory_config["dimension"]
        assert pinecone_memory.metric == memory_config["metric"]

    @pytest.mark.asyncio
    async def test_ensure_index_exists_success(self, pinecone_memory):
        """Test successful index creation when index doesn't exist."""
        with patch.object(pinecone_memory, "pc") as mock_pc:
            mock_pc.list_indexes.return_value = []
            mock_pc.create_index = Mock()

            await pinecone_memory._ensure_index_exists()

            mock_pc.create_index.assert_called_once()

    @pytest.mark.asyncio
    async def test_ensure_index_exists_already_exists(self, pinecone_memory):
        """Test index creation when index already exists."""
        with patch.object(pinecone_memory, "pc") as mock_pc:
            mock_index_info = Mock()
            mock_index_info.name = pinecone_memory.index_name
            mock_pc.list_indexes.return_value = [mock_index_info]
            mock_pc.create_index = Mock()

            await pinecone_memory._ensure_index_exists()

            mock_pc.create_index.assert_not_called()

    @pytest.mark.asyncio
    async def test_get_embedding_success(self, pinecone_memory):
        """Test successful embedding generation."""
        test_text = "This is a test text"
        mock_embedding = [0.1, 0.2, 0.3] * 512  # 1536 dimensions

        with patch("app.memory.pinecone_memory.openai") as mock_openai:
            mock_response = Mock()
            mock_response.data = [Mock(embedding=mock_embedding)]
            mock_openai.embeddings.create.return_value = mock_response

            result = await pinecone_memory._get_embedding(test_text)

            assert result == mock_embedding
            mock_openai.embeddings.create.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_embedding_failure(self, pinecone_memory):
        """Test embedding generation failure."""
        test_text = "This is a test text"

        with patch("app.memory.pinecone_memory.openai") as mock_openai:
            mock_openai.embeddings.create.side_effect = Exception("API error")

            result = await pinecone_memory._get_embedding(test_text)

            assert result == []

    def test_generate_memory_id(self, pinecone_memory):
        """Test memory ID generation."""
        content = "Test content"
        metadata = {"type": "test"}

        memory_id = pinecone_memory._generate_memory_id(content, metadata)

        assert isinstance(memory_id, str)
        assert len(memory_id) > 0

        # Same input should generate same ID
        memory_id2 = pinecone_memory._generate_memory_id(content, metadata)
        assert memory_id == memory_id2

    @pytest.mark.asyncio
    async def test_add_memory_success(
        self, pinecone_memory, sample_memory_content
    ):
        """Test successful memory addition."""
        mock_embedding = [0.1] * 1536

        with patch.object(
            pinecone_memory, "_get_embedding", return_value=mock_embedding
        ), patch.object(pinecone_memory, "_ensure_index_exists"), patch.object(
            pinecone_memory, "pc"
        ) as mock_pc:
            mock_index = Mock()
            mock_pc.Index.return_value = mock_index

            await pinecone_memory.add(sample_memory_content)

            mock_index.upsert.assert_called_once()

    @pytest.mark.asyncio
    async def test_add_memory_no_embedding(
        self, pinecone_memory, sample_memory_content
    ):
        """Test memory addition when embedding generation fails."""
        with patch.object(
            pinecone_memory, "_get_embedding", return_value=[]
        ), patch.object(pinecone_memory, "_ensure_index_exists"), patch.object(
            pinecone_memory, "pc"
        ) as mock_pc:
            mock_index = Mock()
            mock_pc.Index.return_value = mock_index

            await pinecone_memory.add(sample_memory_content)

            # Should not call upsert if no embedding
            mock_index.upsert.assert_not_called()

    @pytest.mark.asyncio
    async def test_query_memory_success(self, pinecone_memory):
        """Test successful memory querying."""
        query_text = "Find similar memories"
        mock_embedding = [0.1] * 1536

        mock_matches = [
            Mock(
                id="mem1",
                score=0.9,
                metadata={
                    "content": "Similar memory 1",
                    "type": "conversation"
                },
            ),
            Mock(
                id="mem2",
                score=0.8,
                metadata={"content": "Similar memory 2", "type": "knowledge"},
            ),
        ]

        with patch.object(
            pinecone_memory, "_get_embedding", return_value=mock_embedding
        ), patch.object(pinecone_memory, "pc") as mock_pc:
            mock_index = Mock()
            mock_query_response = Mock()
            mock_query_response.matches = mock_matches
            mock_index.query.return_value = mock_query_response
            mock_pc.Index.return_value = mock_index

            results = await pinecone_memory.query(query_text, k=2)

            assert len(results) == 2
            assert all(isinstance(r, MemoryContent) for r in results)
            assert results[0].content == "Similar memory 1"
            assert results[1].content == "Similar memory 2"

    @pytest.mark.asyncio
    async def test_query_memory_no_embedding(self, pinecone_memory):
        """Test memory querying when embedding generation fails."""
        query_text = "Find similar memories"

        with patch.object(pinecone_memory, "_get_embedding", return_value=[]):
            results = await pinecone_memory.query(query_text)

            assert results == []

    @pytest.mark.asyncio
    async def test_query_memory_with_metadata_error(self, pinecone_memory):
        """Test memory querying with metadata parsing error."""
        query_text = "Find similar memories"
        mock_embedding = [0.1] * 1536

        mock_matches = [
            Mock(
                id="mem1",
                score=0.9,
                metadata={"content": "Valid memory", "type": "conversation"},
            ),
            Mock(id="mem2", score=0.8, metadata=None),  # Invalid metadata
        ]

        with patch.object(
            pinecone_memory, "_get_embedding", return_value=mock_embedding
        ), patch.object(pinecone_memory, "pc") as mock_pc:
            mock_index = Mock()
            mock_query_response = Mock()
            mock_query_response.matches = mock_matches
            mock_index.query.return_value = mock_query_response
            mock_pc.Index.return_value = mock_index

            results = await pinecone_memory.query(query_text)

            # Should only return valid memories
            assert len(results) == 1
            assert results[0].content == "Valid memory"

    @pytest.mark.asyncio
    async def test_update_context_success(self, pinecone_memory):
        """Test successful context update."""
        mock_context = Mock(spec=ChatCompletionContext)
        mock_context.messages = [
            ChatMessage(content="User message", source="user"),
            ChatMessage(content="Assistant response", source="assistant"),
        ]

        with patch.object(pinecone_memory, "add") as mock_add:
            await pinecone_memory.update_context(mock_context)

            # Should add memories for each message
            assert mock_add.call_count == 2

    @pytest.mark.asyncio
    async def test_update_context_empty_messages(self, pinecone_memory):
        """Test context update with empty messages."""
        mock_context = Mock(spec=ChatCompletionContext)
        mock_context.messages = []

        with patch.object(pinecone_memory, "add") as mock_add:
            await pinecone_memory.update_context(mock_context)

            mock_add.assert_not_called()

    @pytest.mark.asyncio
    async def test_clear_memory_success(self, pinecone_memory):
        """Test successful memory clearing."""
        with patch.object(pinecone_memory, "pc") as mock_pc:
            mock_index = Mock()
            mock_pc.Index.return_value = mock_index

            await pinecone_memory.clear()

            mock_index.delete.assert_called_once_with(
                delete_all=True, namespace=pinecone_memory.namespace
            )

    @pytest.mark.asyncio
    async def test_clear_memory_failure(self, pinecone_memory):
        """Test memory clearing failure."""
        with patch.object(pinecone_memory, "pc") as mock_pc:
            mock_index = Mock()
            mock_index.delete.side_effect = Exception("Delete failed")
            mock_pc.Index.return_value = mock_index

            # Should handle gracefully
            await pinecone_memory.clear()

    @pytest.mark.asyncio
    async def test_close_success(self, pinecone_memory):
        """Test successful memory close."""
        # Should not raise exception
        await pinecone_memory.close()

    @pytest.mark.asyncio
    async def test_get_stats_success(self, pinecone_memory):
        """Test successful stats retrieval."""
        mock_stats = {
            "dimension": 1536,
            "index_fullness": 0.1,
            "namespaces": {pinecone_memory.namespace: {"vector_count": 100}},
        }

        with patch.object(pinecone_memory, "pc") as mock_pc:
            mock_index = Mock()
            mock_index.describe_index_stats.return_value = mock_stats
            mock_pc.Index.return_value = mock_index

            stats = await pinecone_memory.get_stats()

            assert stats["total_vectors"] == 100
            assert stats["dimension"] == 1536
            assert stats["index_fullness"] == 0.1
            assert stats["namespace"] == pinecone_memory.namespace

    @pytest.mark.asyncio
    async def test_get_stats_failure(self, pinecone_memory):
        """Test stats retrieval failure."""
        with patch.object(pinecone_memory, "pc") as mock_pc:
            mock_index = Mock()
            mock_index.describe_index_stats.side_effect = Exception(
                "Stats failed"
            )
            mock_pc.Index.return_value = mock_index

            stats = await pinecone_memory.get_stats()

            # Should return default stats on failure
            assert stats["total_vectors"] == 0
            assert stats["error"] is not None

    @pytest.mark.asyncio
    async def test_memory_content_serialization(self):
        """Test MemoryContent serialization and deserialization."""
        content = MemoryContent(
            content="Test content", metadata={"type": "test", "score": 0.9}
        )

        # Test that content can be accessed
        assert content.content == "Test content"
        assert content.metadata["type"] == "test"
        assert content.metadata["score"] == 0.9

    @pytest.mark.asyncio
    async def test_query_with_custom_k(self, pinecone_memory):
        """Test querying with custom k parameter."""
        query_text = "Find memories"
        mock_embedding = [0.1] * 1536
        custom_k = 5

        with patch.object(
            pinecone_memory, "_get_embedding", return_value=mock_embedding
        ), patch.object(pinecone_memory, "pc") as mock_pc:
            mock_index = Mock()
            mock_query_response = Mock()
            mock_query_response.matches = []
            mock_index.query.return_value = mock_query_response
            mock_pc.Index.return_value = mock_index

            await pinecone_memory.query(query_text, k=custom_k)

            # Verify k parameter was passed correctly
            call_args = mock_index.query.call_args
            assert call_args[1]["top_k"] == custom_k

    @pytest.mark.asyncio
    async def test_add_memory_with_complex_metadata(self, pinecone_memory):
        """Test adding memory with complex metadata."""
        complex_metadata = {
            "type": "conversation",
            "timestamp": "2024-01-01T12:00:00Z",
            "participants": ["user", "assistant"],
            "tags": ["important", "technical"],
            "nested": {"key": "value", "number": 42},
        }

        memory_content = MemoryContent(
            content="Complex memory content", metadata=complex_metadata
        )

        mock_embedding = [0.1] * 1536

        with patch.object(
            pinecone_memory, "_get_embedding", return_value=mock_embedding
        ), patch.object(pinecone_memory, "_ensure_index_exists"), patch.object(
            pinecone_memory, "pc"
        ) as mock_pc:
            mock_index = Mock()
            mock_pc.Index.return_value = mock_index

            await pinecone_memory.add(memory_content)

            # Verify upsert was called with complex metadata
            mock_index.upsert.assert_called_once()
            call_args = mock_index.upsert.call_args[1]
            vectors = call_args["vectors"]
            assert len(vectors) == 1
            assert vectors[0]["metadata"]["type"] == "conversation"
            assert (
                vectors[0]["metadata"]["participants"] == ["user", "assistant"]
            )
