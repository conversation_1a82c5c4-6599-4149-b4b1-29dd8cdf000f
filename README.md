# Agent Platform

A modular Python service for creating and managing AI agent sessions with dynamic tool loading, knowledge retrieval, and streaming responses. The platform is built on Kafka for asynchronous message processing and supports agent-to-agent communication, workflow integration, and comprehensive knowledge management capabilities.

## Project Structure

```
agent-platform/
├── app/                    # Application code
│   ├── autogen_service/    # AutoGen integration
│   │   ├── agent_factory.py       # Agent creation and management
│   │   ├── agent_config_parser.py # Agent configuration parsing
│   │   ├── head_agent.py          # Head agent implementation
│   │   ├── ai_agent.py            # Base AI agent classes
│   │   ├── chat_processor.py      # Chat session processing
│   │   ├── group_chat_factory.py  # Group chat creation
│   │   ├── group_chat_processor.py # Group chat management
│   │   ├── model_factory.py       # Model client creation
│   │   ├── user_agent.py          # User agent implementation
│   │   ├── autogen_mcp.py         # MCP tool integration
│   │   └── agent_fetching.py      # Agent configuration fetching
│   ├── cli/                # Command line interface
│   │   └── chat_cli.py     # CLI chat interface
│   ├── examples/           # Usage examples
│   │   ├── logging_example.py     # Logging system examples
│   │   └── workflow_tool_example.py # Workflow tool examples
│   ├── executer/           # Service execution
│   │   └── run.py          # Service runner
│   ├── helper/             # Helper utilities
│   │   ├── api_call.py     # HTTP request helper
│   │   ├── redis_client.py # Redis client wrapper
│   │   └── session_manager.py # Session management
│   ├── kafka_client/       # Kafka integration
│   │   ├── consumer.py     # Kafka consumer
│   │   └── producer.py     # Kafka producer
│   ├── knowledge/          # Knowledge management
│   │   ├── knowledge_manager.py   # Knowledge retrieval and storage
│   │   └── knowledge_example.py   # Example usage
│   ├── memory/             # Memory management
│   │   ├── pinecone_memory_manager.py # Pinecone integration
│   │   └── pinecone_memory.py         # Pinecone memory implementation
│   ├── schemas/            # Data models
│   │   ├── agent_config.py # Agent configuration models
│   │   ├── api.py          # API models
│   │   ├── kafka.py        # Kafka message models
│   │   └── models.py       # Core Pydantic models
│   ├── services/           # Service layer
│   │   ├── agent_chat.py   # Agent chat service
│   │   ├── agent_fetch.py  # Agent fetching service
│   │   ├── agent_group_service.py # Group chat service
│   │   ├── knowledge_service.py   # Knowledge management service
│   │   └── mcp_service.py  # MCP service integration
│   ├── shared/             # Shared components
│   │   ├── config/         # Configuration management
│   │   │   ├── base.py     # Base settings
│   │   │   ├── environments.py    # Environment configurations
│   │   │   └── logging_config.py  # Logging setup
│   │   ├── session/        # Session management
│   │   │   └── session_manager.py # Session handling
│   │   └── executable_workflow.py # Workflow execution
│   ├── tools/              # Tool implementations
│   │   ├── tool_loader.py          # Base tool loading
│   │   ├── dynamic_tool_loader.py  # Dynamic API tool loading
│   │   ├── dynamic_tool.py         # Dynamic tool implementation
│   │   ├── workflow_tool_loader.py # Workflow tool loading
│   │   ├── mcp_tool_loader.py      # MCP tool loading
│   │   ├── knowledge_tool_loader.py # Knowledge tool loading
│   │   └── streaming_function_tool.py # Streaming tool support
│   └── main.py             # Application entry point
├── config/                 # Configuration files
│   ├── agents.json         # Agent configurations
│   └── agents.py           # Agent configuration helpers
├── docs/                   # Documentation
│   ├── group_chat_implementation.md    # Group chat documentation
│   ├── knowledge_tools_implementation.md # Knowledge tools documentation
│   └── pinecone_memory_implementation.md # Memory implementation docs
├── memory-bank/            # Project memory and context
│   ├── productContext.md   # Project overview and goals
│   ├── activeContext.md    # Current status and focus
│   ├── progress.md         # Task progress tracking
│   ├── decisionLog.md      # Architectural decisions
│   └── systemPatterns.md   # Code and design patterns
├── scripts/                # Utility scripts
│   ├── run_tests.sh        # Test runner
│   ├── initialize_pinecone_memory.py # Memory initialization
│   ├── verify_memory_fix.py # Memory verification
│   └── integration/        # Integration test scripts
│       └── run_kafka_test.py # Kafka integration test
├── tests/                  # Test suite
│   ├── agents/             # Agent tests
│   ├── api/                # API tests
│   ├── shared/             # Shared component tests
│   ├── tools/              # Tool tests
│   ├── test_chat_flow.py   # Chat flow tests
│   ├── test_group_chat_flow.py # Group chat tests
│   └── test_pinecone_memory_*.py # Memory tests
├── .env.example            # Environment variable template
├── .coveragerc             # Coverage configuration
├── .dockerignore           # Docker ignore file
├── .gitignore              # Git ignore file
├── Dockerfile              # Docker configuration
├── pyproject.toml          # Poetry configuration
├── TASK_LIST.md            # Development task tracking
├── TRD.md                  # Technical requirements document
└── README.md               # This file
```

## Features

- **Dynamic Agent Creation**: Create and configure agents on-the-fly with flexible JSON configuration
- **Multi-Agent Communication**: Head Agent delegation system with intelligent task routing
- **Tool Integration**: Support for various tool types (MCP, Workflow, Function, API tools)
- **Kafka-Based Architecture**: Asynchronous message processing for scalable communication
- **Optimized Session Management**: Redis-based session persistence with zlib compression (50-80% size reduction)
- **Comprehensive Logging**: Structured logging with multiple levels, JSON formatting, and component-specific configuration
- **MCP Tool Integration**: Support for Machine Control Program tools with dynamic loading
- **Streaming Tool Support**: Real-time streaming of tool execution results
- **Knowledge Retrieval**: RAG capabilities with ChromaDB and Pinecone vector storage
- **Agent-to-Agent Communication**: Structured messaging system for inter-agent collaboration
- **Head Agent Delegation**: Dynamic task delegation to specialized agents
- **Group Chat Support**: Multi-agent group conversations with configurable termination conditions
- **Memory Management**: Pinecone integration for advanced vector storage and retrieval
- **Multi-Model Support**: Support for OpenAI, Anthropic, Requesty, and OpenRouter model providers
- **File Processing**: Support for PDF, DOC, and PNG file processing with proper SSL handling

## Installation

1. **Clone the Repository:**

   ```bash
   git clone https://gitlab.rapidinnovation.tech/ruh-catalyst/agent-platform.git
   cd agent-platform
   ```

2. **Set Up Environment:**

   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Install Dependencies:**

   ```bash
   poetry install
   ```

4. **Run the Application:**

   ```bash
   # Run in server mode (Kafka consumer) - Primary mode for production
   poetry run python -m app.main --mode server

   # Run in engine mode (Service runner) - Basic service mode
   poetry run python -m app.main --mode engine

   # Quick start using the provided script
   ./run_local.sh
   ```

5. **Using Docker:**

   ```bash
   # Build the Docker image
   docker build -t agent-platform .

   # Run the container
   docker run -p 6000:6000 --env-file .env agent-platform

   # Run with volume for persistent data storage
   docker run -p 6000:6000 -v $(pwd)/data:/app/data --env-file .env agent-platform
   ```

## Architecture

The Agent Platform follows a microservices architecture with Kafka as the central message broker for asynchronous communication between components.

### System Architecture Overview

```mermaid
graph TB
    subgraph "External Services"
        K[Kafka Cluster]
        R[Redis Cluster]
        P[Pinecone]
        C[ChromaDB]
        API[External APIs]
    end

    subgraph "Agent Platform Core"
        KC[Kafka Consumer]
        KP[Kafka Producer]
        AF[Agent Factory]
        CP[Chat Processor]
        SM[Session Manager]
        TL[Tool Loaders]
        KM[Knowledge Manager]
        MM[Memory Manager]
        MF[Model Factory]
    end

    subgraph "Agent Layer"
        HA[Head Agent]
        AA[Assistant Agents]
        UA[User Agent]
        GA[Group Agents]
    end

    K --> KC
    KP --> K
    KC --> AF
    AF --> AA
    AF --> HA
    AF --> UA
    CP --> SM
    SM --> R
    AF --> TL
    TL --> API
    KM --> C
    KM --> P
    MM --> P
    MF --> AA
    HA --> GA
    CP --> KP
```

### Core Components

- **Agent Factory**: Central component for creating and managing agent instances
- **Head Agent**: Intelligent delegation system that routes tasks to specialized agents
- **Chat Processor**: Handles chat sessions and message processing
- **Group Chat Factory**: Creates and manages multi-agent group conversations
- **Tool Loaders**: Modular system for loading different types of tools (MCP, Workflow, Dynamic, Knowledge)
- **Knowledge Manager**: RAG implementation with vector storage integration
- **Session Manager**: Redis-based session persistence with compression
- **Kafka Consumer/Producer**: Handles asynchronous message processing and communication

### Agent Types

1. **Head Agent**: Central coordinator that delegates tasks to specialized agents
2. **Assistant Agent**: General-purpose agent with customizable tools and capabilities
3. **Specialized Agents**: Domain-specific agents (Marketing, Sales, Research, etc.) with dedicated tools
4. **User Agent**: Represents user interactions in multi-agent conversations

### Tool System

The platform supports multiple tool types through a modular loading system:

1. **MCP Tools**: Machine Control Program tools for external service integration
2. **Workflow Tools**: Execute workflows in external workflow systems
3. **Dynamic Tools**: API-based tools with JSON payload support
4. **Knowledge Tools**: RAG-enabled tools for knowledge retrieval
5. **Function Tools**: Direct Python function execution
6. **Streaming Tools**: Real-time streaming of tool execution results

### Session Management Optimizations

The platform includes optimized session management with:

1. **Memory Compression**: Uses zlib compression to reduce storage size by 50-80%
2. **Message Window Management**: Maintains a configurable window of recent messages
3. **Selective Configuration Storage**: Stores only essential agent configuration data
4. **Binary Storage**: Efficient binary data handling for compressed messages
5. **Efficient Session Listing**: Uses Redis SCAN for better performance

Configuration options:

```python
session_manager = SessionManager(
    redis_client,
    session_ttl=3600,        # 1 hour TTL
    max_messages=50,         # Keep only 50 messages per session
    compression_level=6      # Compression level (0-9)
)
```

## Development

### Adding New Agents

1. Create agent configuration in `config/agents.json`
2. Implement agent class extending `AIAgent` or `RoutedAgent`
3. Register agent in the agent factory
4. Add agent-specific tools and workflows

### Creating Custom Tools

1. Define tool schema and configuration
2. Implement tool logic in appropriate tool loader:
   - `DynamicToolLoader` for API tools
   - `WorkflowToolLoader` for workflow tools
   - `McpToolLoader` for MCP tools
   - `KnowledgeToolLoader` for knowledge tools

### Knowledge Management

Add knowledge to agents using the Knowledge Service:

```python
from app.services.knowledge_service import KnowledgeService

knowledge_service = KnowledgeService()

await knowledge_service.add_knowledge_to_agent(
    agent_id="agent_name",
    agent=agent_instance,
    knowledge_sources={
        "texts": ["Knowledge content"],
        "urls": ["https://example.com"],
        "documents": ["/path/to/document.pdf"]
    }
)
```

### Configuration Management

The application uses environment variables for configuration:

- API settings and model credentials
- Kafka and Redis connection details
- Workflow API endpoints
- Agent configuration parameters
- Logging configuration

### Message Flow Architecture

```mermaid
sequenceDiagram
    participant Client
    participant Kafka
    participant Consumer
    participant AgentFactory
    participant Agent
    participant Tools
    participant Redis
    participant VectorDB

    Client->>Kafka: Send chat request
    Kafka->>Consumer: Consume message
    Consumer->>Redis: Get/Create session
    Consumer->>AgentFactory: Create agent
    AgentFactory->>VectorDB: Load knowledge
    AgentFactory->>Tools: Load tools
    AgentFactory->>Agent: Initialize agent
    Agent->>Tools: Execute tools (if needed)
    Agent->>VectorDB: Query knowledge (if needed)
    Agent->>Consumer: Generate response
    Consumer->>Redis: Update session
    Consumer->>Kafka: Publish response
    Kafka->>Client: Deliver response
```

### Kafka Topics

The platform uses several Kafka topics for different types of communication:

- `agent_creation_requests`: Agent creation requests
- `agent_chat_requests`: Chat message requests
- `agent_chat_responses`: Chat message responses
- `orchestration_team_session`: Orchestration team session management
- `orchestration_team_chat`: Orchestration team chat messages
- `human_input_request`: Human input requests
- `human_input_response`: Human input responses
- `agent_chat_stop`: Chat stop signals
- `token_usage`: Token usage tracking

## Model Providers

The platform supports multiple AI model providers with seamless switching via environment variables. This allows you to use different providers based on your needs, budget, and model availability.

### Supported Providers

#### 1. Requesty (Default)

- **Access**: 300+ models from multiple providers through a single API
- **Benefits**: Unified pricing, automatic failover, usage analytics
- **Best for**: Production environments requiring reliability and cost optimization

#### 2. OpenRouter

- **Access**: 200+ models from multiple providers
- **Benefits**: Competitive pricing, model routing, fallbacks
- **Best for**: Developers wanting access to latest models with flexible routing

#### 3. OpenAI Direct

- **Access**: Latest OpenAI models (GPT-4, GPT-3.5, etc.)
- **Benefits**: Direct API access, full feature support, highest reliability
- **Best for**: Applications specifically requiring OpenAI models

### Configuration

Set your preferred provider using the `MODEL_PROVIDER` environment variable:

```bash
# Use Requesty (default)
export MODEL_PROVIDER=requesty
export REQUESTY_API_KEY=your-requesty-api-key

# Use OpenRouter
export MODEL_PROVIDER=openrouter
export OPENROUTER_API_KEY=your-openrouter-api-key
export OPENROUTER_SITE_URL=https://yoursite.com  # Optional
export OPENROUTER_SITE_NAME="Your App Name"      # Optional

# Use OpenAI directly
export MODEL_PROVIDER=openai
export OPENAI_API_KEY=your-openai-api-key
```

### Model Selection

Each provider uses different model naming conventions:

```python
# Requesty format
model = "gpt-4o-mini"  # Provider prefix added automatically

# OpenRouter format
model = "openai/gpt-4o-mini"           # OpenAI models
model = "anthropic/claude-3-sonnet"    # Anthropic models
model = "google/gemini-1.5-pro"       # Google models

# OpenAI Direct format
model = "gpt-4o-mini"  # Standard OpenAI model names
```

### Usage Examples

#### Basic Model Client Creation

```python
from app.autogen_service.model_factory import ModelFactory

# Configuration automatically uses the selected provider
config = {
    "provider": "OpenAIChatCompletionClient",
    "model": "gpt-4o-mini",  # Format depends on selected provider
    "temperature": 0.7,
    "max_tokens": 1000,
}

client = ModelFactory.create_model_client(config)
```

#### Provider-Specific Features

```python
# OpenRouter with site attribution
config = {
    "provider": "OpenAIChatCompletionClient",
    "model": "anthropic/claude-3-sonnet",
    # Site info automatically added from environment
}

# Requesty with automatic model routing
config = {
    "provider": "OpenAIChatCompletionClient",
    "model": "gpt-4o",  # Automatically routed through Requesty
}
```

#### Checking Model Availability

```python
# Check if a model is supported by current provider
is_supported = ModelFactory.is_model_supported("gpt-4o-mini")

# Get all available models
models = await ModelFactory.fetch_requesty_models()  # For Requesty
models = await ModelFactory.fetch_openrouter_models()  # For OpenRouter
```

### Testing Different Providers

Run the provider switching example:

```bash
cd app/examples
python provider_switching_example.py
```

This will test all configured providers and show their capabilities.

## Testing

### Running Tests

1. **Using the test script:**

   ```bash
   ./scripts/run_tests.sh
   ```

2. **Running directly with pytest:**

   ```bash
   PYTHONPATH=$PYTHONPATH:. poetry run pytest
   ```

3. **With coverage report:**

   ```bash
   PYTHONPATH=$PYTHONPATH:. poetry run pytest --cov=app --cov-report=html --cov-report=term-missing
   ```

### Test Coverage

- HTML report generated in `coverage_html/` directory
- Comprehensive test suite covering agents, tools, services, and integrations
- Performance and stress testing included

## Logging

The application uses a comprehensive structured logging system:

### Configuration

```bash
# Enable multiple log levels
LOG_LEVEL=DEBUG,INFO,WARNING,ERROR,CRITICAL

# Set component-specific log levels
KAFKA_LOG_LEVEL=DEBUG
AGENT_LOG_LEVEL=INFO
SESSION_LOG_LEVEL=WARNING
REDIS_LOG_LEVEL=ERROR

# Enable JSON formatting
LOG_FORMAT=json

# Set log directory
LOGS_DIR=custom_logs
```

### Usage

```python
from app.shared.config.logging_config import get_logger, log_with_context

logger = get_logger(__name__)
logger.info("Application started")

# Log with context
log_with_context(
    logger,
    logging.INFO,
    "User action",
    extra={"user_id": "123", "session_id": "456"}
)
```

## Memory Bank

The project includes a comprehensive Memory Bank system for maintaining project context:

- **productContext.md**: Project overview, goals, and architecture
- **activeContext.md**: Current status, recent changes, and open questions
- **progress.md**: Task progress tracking and development priorities
- **decisionLog.md**: Architectural decisions with rationale
- **systemPatterns.md**: Code patterns and design standards

## Usage Examples

### Basic Agent Chat via Kafka

```python
from app.kafka_client.producer import KafkaProducer
from app.kafka_client.consumer import KafkaConsumer

# Send a chat message
producer = KafkaProducer()
await producer.send_message("agent_chat_requests", {
    "session_id": "session-123",
    "user_message": "Hello, how can you help me?",
    "agent_config": {...}
})

# Listen for responses
consumer = KafkaConsumer()
async for message in consumer.consume("agent_chat_responses"):
    print(f"Agent response: {message}")
```

### CLI Chat Interface

```bash
# Use the CLI for interactive chat
poetry run python -m app.cli.chat_cli
```

## Contributing

1. Fork the repository
2. Create feature branch
3. Implement changes with tests
4. Update documentation
5. Submit pull request

## License

[Specify License]

## Acknowledgements

- Managed using [Poetry](https://python-poetry.org/)
- Uses [Microsoft AutoGen](https://github.com/microsoft/autogen) for agent framework
- Integrates with [Kafka](https://kafka.apache.org/) for message processing
- Uses [Redis](https://redis.io/) for session management
- Uses [ChromaDB](https://www.trychroma.com/) and [Pinecone](https://www.pinecone.io/) for vector storage
- Integrates with [Anthropic Claude](https://www.anthropic.com/), [OpenAI](https://openai.com/), [Requesty](https://requesty.ai/), and [OpenRouter](https://openrouter.ai/) for model access
