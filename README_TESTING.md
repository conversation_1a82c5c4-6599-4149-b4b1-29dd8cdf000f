# Testing Guide for Agent Platform

This document provides comprehensive information about running tests and generating coverage reports for the Agent Platform project.

## Quick Start

### Run All Tests with Coverage
```bash
# Using Poetry (recommended)
poetry run python scripts/test.py --coverage

# Using comprehensive Python script
python scripts/run_tests.py

# Using shell script
./scripts/run_tests.sh
```

### Run Tests for Specific Module
```bash
# Using Poetry (recommended)
poetry run python scripts/test.py --module kafka_client --coverage

# Using comprehensive script
python scripts/run_tests.py --module kafka_client

# Test only memory components
poetry run python scripts/test.py --module memory --html

# Test only helper utilities
poetry run python scripts/test.py --module helper --verbose
```

### Run Specific Test File
```bash
# Using Poetry
poetry run python scripts/test.py --file tests/test_model_factory.py --coverage

# Using comprehensive script
python scripts/run_tests.py --test-file tests/test_model_factory.py
```

## Test Runner Options

### Poetry-based Test Runner (Recommended)

#### Basic Usage
```bash
# Run all tests
poetry run python scripts/test.py

# Run with coverage
poetry run python scripts/test.py --coverage

# Run with HTML coverage report
poetry run python scripts/test.py --html

# Run with XML coverage report (for CI/CD)
poetry run python scripts/test.py --xml
```

#### Module-specific Testing
```bash
# Test specific module
poetry run python scripts/test.py --module kafka_client --coverage

# Test with verbose output
poetry run python scripts/test.py --module memory --verbose --html

# Stop on first failure
poetry run python scripts/test.py --module schemas --fail-fast
```

### Comprehensive Test Runner Features

#### Coverage Reporting Options

##### Default (HTML + Terminal)
```bash
python scripts/run_tests.py
```
- Generates HTML coverage report in `coverage_html/`
- Shows coverage summary in terminal

##### HTML Report Only
```bash
python scripts/run_tests.py --html-only
```
- Generates only HTML coverage report
- Open `coverage_html/index.html` in browser

##### Terminal Report Only
```bash
python scripts/run_tests.py --terminal-only
```
- Shows coverage only in terminal
- No HTML files generated

##### Additional Report Formats
```bash
# Generate XML report (useful for CI/CD)
python scripts/run_tests.py --xml-report

# Generate JSON report
python scripts/run_tests.py --json-report

# Generate both XML and JSON
python scripts/run_tests.py --xml-report --json-report
```

### Test Execution Options

#### Verbose Output
```bash
python scripts/run_tests.py --verbose
```

#### Stop on First Failure
```bash
python scripts/run_tests.py --fail-fast
```

#### Show Print Statements
```bash
python scripts/run_tests.py --capture no
```

#### Run Without Coverage
```bash
python scripts/run_tests.py --no-coverage
```

## Available Test Modules

The following test modules are available:

- **`kafka_client`** - Kafka producer and consumer tests
- **`memory`** - Pinecone memory management tests
- **`helper`** - Redis client and utility tests
- **`schemas`** - Pydantic schema validation tests
- **`autogen_service`** - AutoGen agent factory tests
- **`agents`** - Agent-specific tests
- **`api`** - API endpoint tests
- **`services`** - Service layer tests
- **`shared`** - Shared configuration tests
- **`tools`** - Tool loading and MCP tests

## Coverage Configuration

### Coverage Settings (`.coveragerc`)
```ini
[run]
source = app
omit =
    */tests/*
    */migrations/*
    */__init__.py
    */settings.py

[report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    raise NotImplementedError
    if __name__ == .__main__.:
    pass
    raise ImportError

[html]
directory = coverage_html
```

### Pytest Configuration (`pyproject.toml`)
```toml
[tool.pytest.ini_options]
asyncio_mode = "auto"
testpaths = ["tests"]
python_files = ["test_*.py"]
addopts = "-v --cov=app --cov-report=html --cov-report=term-missing"
pythonpath = ["."]
```

## Advanced Usage Examples

### Complete Test Suite with All Reports
```bash
python scripts/run_tests.py --verbose --xml-report --json-report
```

### Quick Module Test
```bash
python scripts/run_tests.py --module kafka_client --terminal-only --fail-fast
```

### CI/CD Pipeline Test
```bash
python scripts/run_tests.py --xml-report --no-coverage --fail-fast
```

### Development Testing
```bash
python scripts/run_tests.py --verbose --capture no --keep-coverage
```

## Output Files

### Coverage Reports
- **HTML Report**: `coverage_html/index.html` - Interactive HTML coverage report
- **XML Report**: `coverage.xml` - XML format for CI/CD integration
- **JSON Report**: `coverage.json` - JSON format for programmatic analysis
- **Coverage Data**: `.coverage` - Raw coverage data file

### Test Results
- Test results are displayed in terminal
- Failed tests show detailed error information
- Coverage percentages shown for each module

## Troubleshooting

### Common Issues

#### Poetry Not Found
```bash
# Install Poetry
curl -sSL https://install.python-poetry.org | python3 -

# Or using pip
pip install poetry
```

#### Dependencies Not Installed
```bash
# Install all dependencies
poetry install

# Install only main dependencies
poetry install --no-dev
```

#### Permission Denied
```bash
# Make scripts executable
chmod +x scripts/run_tests.py
chmod +x scripts/run_tests.sh
```

#### Module Not Found
```bash
# Check available test modules
ls tests/

# Run from project root
cd /path/to/agent-platform
python scripts/run_tests.py
```

### Environment Setup

#### Using Poetry (Recommended)
```bash
# Install dependencies
poetry install

# Run tests in Poetry environment
poetry run python scripts/run_tests.py
```

#### Using Virtual Environment
```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt  # If available
# Or install manually: pip install pytest pytest-cov coverage

# Run tests
python scripts/run_tests.py
```

## Integration with IDEs

### VS Code
Add to `.vscode/settings.json`:
```json
{
    "python.testing.pytestEnabled": true,
    "python.testing.pytestArgs": [
        "tests"
    ],
    "python.testing.cwd": "${workspaceFolder}"
}
```

### PyCharm
1. Go to Settings → Tools → Python Integrated Tools
2. Set Default test runner to pytest
3. Set Working directory to project root

## Continuous Integration

### GitHub Actions Example
```yaml
- name: Run Tests
  run: |
    python scripts/run_tests.py --xml-report --fail-fast
    
- name: Upload Coverage
  uses: codecov/codecov-action@v3
  with:
    file: ./coverage.xml
```

### GitLab CI Example
```yaml
test:
  script:
    - python scripts/run_tests.py --xml-report
  coverage: '/TOTAL.*\s+(\d+%)$/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage.xml
```

## Best Practices

1. **Run tests before committing**: Always run the full test suite before pushing changes
2. **Use module-specific testing**: During development, run tests for specific modules to save time
3. **Monitor coverage**: Aim for >80% code coverage on critical components
4. **Fix failing tests immediately**: Don't let broken tests accumulate
5. **Use verbose output**: When debugging test failures, use `--verbose` flag
6. **Keep coverage data**: Use `--keep-coverage` when running multiple test sessions

## Support

For issues with the test runner or coverage reporting:

1. Check that all dependencies are installed: `poetry install`
2. Verify you're in the project root directory
3. Check that test files follow the naming convention: `test_*.py`
4. Ensure Python version compatibility (3.11+)

For more information about the project structure and testing patterns, see the Memory Bank documentation in `memory-bank/`.